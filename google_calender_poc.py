import os
import time
from datetime import datetime, timezone

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# --- CONFIGURATION ---
# The permissions your script is requesting.
SCOPES = ["https://www.googleapis.com/auth/calendar.readonly"]
# How often to check for new events (in seconds).
POLL_INTERVAL_SECONDS = 15
# The file to store our state (the timestamp of the last processed event).
STATE_FILE = "last_check_timestamp.txt"


def authenticate():
    """Handles user authentication and returns an authorized service object."""
    creds = None
    # The file token.json stores the user's access and refresh tokens, and is
    # created automatically when the authorization flow completes for the first time.
    if os.path.exists("token.json"):
        creds = Credentials.from_authorized_user_file("token.json", SCOPES)

    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            print("Refreshing expired credentials...")
            creds.refresh(Request())
        else:
            # This will open a browser window for you to log in and authorize the script.
            from google_auth_oauthlib.flow import InstalledAppFlow

            flow = InstalledAppFlow.from_client_secrets_file(
                "data/credentials.json", SCOPES
            )
            creds = flow.run_local_server(port=0)

        # Save the credentials for the next run
        with open("token.json", "w") as token:
            token.write(creds.to_json())
            print("Credentials saved to token.json")

    return build("calendar", "v3", credentials=creds)


def save_last_check_time(timestamp_str: str):
    """Saves the last checked timestamp to the state file."""
    with open(STATE_FILE, "w") as f:
        f.write(timestamp_str)


def load_last_check_time() -> str:
    """Loads the last checked timestamp from the state file."""
    if not os.path.exists(STATE_FILE):
        # On the first run, we check for events created in the last 5 minutes.
        now_utc = datetime.now(timezone.utc)
        return now_utc.isoformat()

    with open(STATE_FILE, "r") as f:
        return f.read().strip()


def get_new_calendar_events(service, last_check_time_str: str):
    """
    Fetches events created after the last check time.
    Google API requires RFC3339 timestamp format (e.g., '2023-10-27T10:00:00Z').
    """
    try:
        events_result = (
            service.events()
            .list(
                calendarId="primary",
                timeMin=last_check_time_str,  # Find events that *start* after this time
                # To find events *created* after a certain time, you would need
                # a more complex logic of fetching and checking the 'created' field.
                # For a POC, `timeMin` is a good proxy for "newness".
                maxResults=10,
                singleEvents=True,
                orderBy="startTime",
            )
            .execute()
        )
        events = events_result.get("items", [])
        return events
    except HttpError as error:
        print(f"An error occurred: {error}")
        return []


def main_loop():
    """The main polling loop for the trigger."""
    print("--- Google Calendar Trigger POC ---")
    print("Authenticating...")
    service = authenticate()
    print("Authentication successful. Starting polling loop...")

    # We will use the 'created' timestamp to find new events.
    # We fetch all upcoming events and filter by 'created' time.
    # This is more accurate than using timeMin for a "new event" trigger.

    while True:
        try:
            last_check_time_str = load_last_check_time()
            last_check_time_dt = datetime.fromisoformat(
                last_check_time_str.replace("Z", "+00:00")
            )

            print(
                f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking for new events created after {last_check_time_str}..."
            )

            # We fetch all upcoming events and then filter by their creation time.
            now_utc = datetime.now(timezone.utc).isoformat()
            events_result = (
                service.events()
                .list(
                    calendarId="primary",
                    timeMin=now_utc,
                    singleEvents=True,
                    orderBy="startTime",
                )
                .execute()
            )

            all_upcoming_events = events_result.get("items", [])
            newly_created_events = []

            for event in all_upcoming_events:
                created_str = event["created"]
                # Google's timestamp has a 'Z' which python < 3.11 struggles with.
                created_dt = datetime.fromisoformat(created_str.replace("Z", "+00:00"))
                if created_dt > last_check_time_dt:
                    newly_created_events.append(event)

            if not newly_created_events:
                print("No new events found.")
            else:
                print(f"Found {len(newly_created_events)} new event(s)!")
                latest_creation_time = last_check_time_dt

                for event in newly_created_events:
                    summary = event["summary"]
                    start = event["start"].get("dateTime", event["start"].get("date"))

                    # THIS IS THE "ACTION" PART OF THE TRIGGER
                    print(
                        f"  -> ACTION: Triggering for event: '{summary}' starting at {start}"
                    )

                    # Update the latest creation time we've seen in this batch
                    event_created_dt = datetime.fromisoformat(
                        event["created"].replace("Z", "+00:00")
                    )
                    if event_created_dt > latest_creation_time:
                        latest_creation_time = event_created_dt

                # Save the timestamp of the newest event we just processed
                # Add a microsecond to avoid re-fetching the same event
                new_state_time = (
                    (latest_creation_time).isoformat().replace("+00:00", "Z")
                )
                save_last_check_time(new_state_time)
                print(
                    f"State updated. Will now look for events created after {new_state_time}"
                )

            time.sleep(POLL_INTERVAL_SECONDS)

        except KeyboardInterrupt:
            print("\nShutting down trigger.")
            break
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            time.sleep(POLL_INTERVAL_SECONDS * 2)  # Wait longer after an error


if __name__ == "__main__":
    main_loop()
