#!/usr/bin/env python3
"""
Debug script to test Google Calendar webhook setup
"""

import requests
import json
from datetime import datetime

# Configuration
NGROK_URL = "https://99b5-103-173-221-201.ngrok-free.app"
FASTAPI_ENDPOINT = f"{NGROK_URL}/api/v1/webhooks/google-calendar"
VERIFY_ENDPOINT = f"{NGROK_URL}/api/v1/webhooks/google-calendar/verify"
STATUS_ENDPOINT = f"{NGROK_URL}/api/v1/webhooks/status"


def test_webhook_endpoints():
    """Test all webhook endpoints to identify issues"""

    print("🔍 Google Calendar Webhook Diagnostic Tool")
    print("=" * 50)
    print(f"Testing ngrok URL: {NGROK_URL}")
    print(f"Timestamp: {datetime.now()}")
    print()

    # Test 1: Basic connectivity
    print("1. Testing basic connectivity...")
    try:
        response = requests.get(STATUS_ENDPOINT, timeout=10)
        print(f"   ✅ Status endpoint: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   ❌ Status endpoint failed: {e}")
        return False

    print()

    # Test 2: Webhook verification endpoint
    print("2. Testing webhook verification endpoint...")
    try:
        response = requests.get(VERIFY_ENDPOINT, timeout=10)
        print(f"   ✅ Verify endpoint: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   ❌ Verify endpoint failed: {e}")

    print()

    # Test 3: Simulate Google Calendar webhook
    print("3. Simulating Google Calendar webhook...")
    try:
        # Simulate a Google Calendar webhook notification
        headers = {
            "Content-Type": "application/json",
            "X-Goog-Channel-ID": "test-channel-123",
            "X-Goog-Resource-State": "exists",
            "X-Goog-Resource-ID": "test-resource-456",
            "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
            "User-Agent": "APIs-Google; (+https://developers.google.com/webmasters/APIs-Google.html)",
        }

        test_payload = {"type": "test", "message": "Test webhook from debug script"}

        response = requests.post(
            FASTAPI_ENDPOINT, json=test_payload, headers=headers, timeout=10
        )
        print(f"   ✅ Webhook POST: {response.status_code}")
        print(f"   Response: {response.json()}")

    except Exception as e:
        print(f"   ❌ Webhook POST failed: {e}")

    print()

    # Test 4: Check for active webhook subscriptions
    print("4. Checking for active webhook subscriptions...")
    try:
        # This would require Google Calendar API access
        print("   ℹ️  To check active subscriptions, you need to:")
        print("      - Use Google Calendar API to list active channels")
        print("      - Check if any channels point to your webhook URL")
        print("      - Verify channel expiration times")

    except Exception as e:
        print(f"   ❌ Subscription check failed: {e}")

    print()
    print("🎯 DIAGNOSIS SUMMARY:")
    print("=" * 50)

    # Check if webhook URL is properly configured
    if NGROK_URL in ["https://99b5-103-173-221-201.ngrok-free.app"]:
        print("✅ Webhook URL is configured")
    else:
        print("❌ Webhook URL needs to be updated")

    print()
    print("📋 NEXT STEPS:")
    print("1. Ensure Google Calendar webhook is registered with this URL:")
    print(f"   {FASTAPI_ENDPOINT}")
    print()
    print("2. Check if you have active webhook subscriptions:")
    print("   - Use Google Calendar API to list channels")
    print("   - Verify they point to the correct URL")
    print()
    print("3. Test webhook registration:")
    print("   - Create a new webhook subscription")
    print("   - Verify it receives notifications")

    return True


if __name__ == "__main__":
    test_webhook_endpoints()
