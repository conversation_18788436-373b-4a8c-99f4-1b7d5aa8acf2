#!/usr/bin/env python3
"""
Check and setup Google Calendar webhook subscriptions
"""

import os
import json
import uuid
from datetime import datetime, timezone
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Configuration
WEBHOOK_URL = (
    "https://99b5-103-173-221-201.ngrok-free.app/api/v1/webhooks/google-calendar"
)
CALENDAR_ID = "primary"
WEBHOOK_TTL = 604800  # 7 days


def authenticate():
    """Authenticate with Google Calendar API"""
    creds = None

    # Look for token file
    token_paths = ["token.json", "./token.json", "../token.json"]
    token_file = None

    for path in token_paths:
        if os.path.exists(path):
            token_file = path
            break

    if not token_file:
        print(
            "❌ No token.json file found. Please ensure you have Google Calendar credentials."
        )
        print("   Searched paths:", token_paths)
        return None

    print(f"📁 Loading credentials from: {token_file}")

    try:
        creds = Credentials.from_authorized_user_file(token_file)

        # Check if credentials need refresh
        if not creds.valid:
            if creds.expired and creds.refresh_token:
                print("🔄 Refreshing expired credentials...")
                creds.refresh(Request())

                # Save refreshed credentials
                with open(token_file, "w") as token:
                    token.write(creds.to_json())
                print("✅ Credentials refreshed and saved")
            else:
                print("❌ Credentials are invalid and cannot be refreshed")
                return None

        return build("calendar", "v3", credentials=creds)

    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return None


def list_active_channels(service):
    """List active webhook channels (Note: Google doesn't provide a direct API for this)"""
    print("📋 Checking for active webhook channels...")
    print(
        "   ℹ️  Google Calendar API doesn't provide a direct way to list active channels."
    )
    print("   ℹ️  You need to track channels manually or check your application logs.")
    return []


def create_webhook_subscription(service):
    """Create a new webhook subscription"""
    try:
        print(f"🔧 Creating webhook subscription...")
        print(f"   Webhook URL: {WEBHOOK_URL}")
        print(f"   Calendar ID: {CALENDAR_ID}")

        # Generate unique channel ID
        channel_id = f"debug-{str(uuid.uuid4())[:8]}"

        # Calculate expiration
        expiration = int((datetime.now(timezone.utc).timestamp() + WEBHOOK_TTL) * 1000)

        # Create watch request
        watch_request = {
            "id": channel_id,
            "type": "web_hook",
            "address": WEBHOOK_URL,
            "expiration": str(expiration),
        }

        print(f"   Channel ID: {channel_id}")
        print(f"   Expiration: {datetime.fromtimestamp(expiration/1000, timezone.utc)}")

        # Execute the watch request
        result = (
            service.events().watch(calendarId=CALENDAR_ID, body=watch_request).execute()
        )

        print("✅ Webhook subscription created successfully!")
        print(f"   Channel ID: {result.get('id')}")
        print(f"   Resource ID: {result.get('resourceId')}")
        print(
            f"   Expiration: {datetime.fromtimestamp(int(result.get('expiration', 0))/1000, timezone.utc)}"
        )

        return result

    except HttpError as e:
        print(f"❌ HTTP Error creating webhook: {e}")
        if e.resp.status == 400:
            print("   💡 This might be because:")
            print("      - The webhook URL is not accessible")
            print("      - The URL is not HTTPS")
            print("      - Google cannot reach your ngrok tunnel")
        elif e.resp.status == 401:
            print("   💡 Authentication error - check your credentials")
        elif e.resp.status == 403:
            print("   💡 Permission error - check your OAuth scopes")
        return None

    except Exception as e:
        print(f"❌ Error creating webhook: {e}")
        return None


def test_webhook_url():
    """Test if the webhook URL is accessible"""
    print("🌐 Testing webhook URL accessibility...")

    try:
        import requests

        response = requests.get(
            f"{WEBHOOK_URL.replace('/google-calendar', '/status')}", timeout=10
        )
        if response.status_code == 200:
            print("✅ Webhook URL is accessible")
            return True
        else:
            print(f"❌ Webhook URL returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot reach webhook URL: {e}")
        return False


def main():
    print("🔍 Google Calendar Webhook Diagnostic Tool")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print()

    # Step 1: Test webhook URL
    if not test_webhook_url():
        print("\n❌ CRITICAL: Webhook URL is not accessible!")
        print("   Please ensure:")
        print("   1. Your FastAPI server is running on port 8000")
        print("   2. Your ngrok tunnel is active")
        print("   3. The ngrok URL in the script matches your actual URL")
        return

    print()

    # Step 2: Authenticate
    service = authenticate()
    if not service:
        print("\n❌ CRITICAL: Cannot authenticate with Google Calendar API!")
        return

    print("✅ Successfully authenticated with Google Calendar API")
    print()

    # Step 3: List active channels (limited info available)
    list_active_channels(service)
    print()

    # Step 4: Create new webhook subscription
    print("🚀 Creating new webhook subscription...")
    result = create_webhook_subscription(service)

    if result:
        print("\n🎉 SUCCESS! Webhook subscription is now active.")
        print("\n📋 Next steps:")
        print("1. Create or modify a calendar event to test the webhook")
        print("2. Check your FastAPI server logs for incoming webhook notifications")
        print("3. Monitor the webhook at: http://localhost:4040 (ngrok web interface)")

        print(f"\n💾 Save this channel info for cleanup:")
        print(f"   Channel ID: {result.get('id')}")
        print(f"   Resource ID: {result.get('resourceId')}")

    else:
        print("\n❌ FAILED to create webhook subscription!")
        print("\n🔧 Troubleshooting steps:")
        print("1. Verify your ngrok tunnel is running and accessible")
        print("2. Check that your Google Calendar API credentials are valid")
        print("3. Ensure your OAuth app has the correct scopes")
        print("4. Try creating a calendar event manually to test")


if __name__ == "__main__":
    main()
