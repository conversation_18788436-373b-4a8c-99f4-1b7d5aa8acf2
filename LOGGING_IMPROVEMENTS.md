# Logging System Improvements

The logging system has been significantly improved to make it much more readable and useful for development and debugging. This document explains the changes and how to use the new logging features.

## 🔧 What Was Fixed

### **Before (Unreadable JSON)**
```json
{"event": "Setting up Google Calendar trigger", "trigger_id": "abc-123", "calendar_id": "primary", "event_types": ["created", "updated"], "service": "trigger-service", "version": "0.1.0", "level": "info", "timestamp": "2025-06-12T14:25:06.398148Z"}
```

### **After (Readable Text)**
```
[14:25:06]     INFO src.adapters.google_calendar | 🔧 Setting up Google Calendar trigger
    👤 User: user_id=alice
    🎯 Trigger: trigger_id=abc-123 | calendar_id=primary | event_types=['created', 'updated']
```

## 🎨 New Logging Features

### **1. Colorized Output with Emojis**
- **🚀 INFO**: Green text with relevant emojis
- **⚠️ WARNING**: Yellow text for warnings
- **❌ ERROR**: Red text for errors
- **🔍 DEBUG**: Cyan text for debug info
- **🔥 CRITICAL**: Magenta text for critical issues

### **2. Structured Context Grouping**
Information is automatically grouped by category:
- **👤 User**: User-related information (user_id, authenticated_user)
- **🎯 Trigger**: Trigger-related information (trigger_id, trigger_type, calendar_id)
- **❌ Error**: Error information (error_type, error_message, exception)
- **📋 Details**: Other contextual information

### **3. Reduced Noise**
Third-party library logs are automatically silenced:
- `httpx`, `httpcore`, `urllib3` (HTTP libraries)
- `google`, `googleapiclient` (Google API libraries)
- `uvicorn.access` (Web server access logs)

### **4. Enhanced Google Calendar Logging**
Specific improvements for Google Calendar operations:
- **🔧 Setup operations** with user and trigger context
- **🔄 Polling status** with intervals and calendar info
- **📅 Event detection** with event counts and details
- **🎯 Trigger events** with clear action indicators
- **✅/❌ Success/failure** indicators with context

## ⚙️ Configuration

### **Environment Variables**
```env
# Development (readable text format)
LOG_LEVEL=DEBUG
LOG_FORMAT=text

# Production (machine-readable JSON)
LOG_LEVEL=INFO
LOG_FORMAT=json
```

### **Log Levels**
- **DEBUG**: Detailed information for debugging
- **INFO**: General operational information
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failures
- **CRITICAL**: Critical errors that may stop the service

## 📋 Usage Examples

### **Basic Logging**
```python
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Simple message
logger.info("Operation completed")

# With context
logger.info("User operation", 
           user_id="alice",
           operation="login",
           success=True)
```

### **Google Calendar Specific Logging**
```python
# Trigger setup
logger.info("🔧 Setting up Google Calendar trigger",
           trigger_id=trigger_id,
           user_id=user_id,
           calendar_id="primary",
           use_polling=True)

# Event detection
logger.info("📅 Found new calendar events",
           trigger_id=trigger_id,
           user_id=user_id,
           event_count=3,
           calendar_id="primary")

# Trigger event
logger.info("🎯 TRIGGER EVENT FIRED",
           event_type="created",
           event_title="Team Meeting",
           trigger_id=trigger_id,
           detection_method="polling")
```

### **Error Logging**
```python
# Simple error
logger.error("❌ Failed to get credentials",
            user_id=user_id,
            trigger_id=trigger_id)

# Detailed error
logger.error("❌ Google Calendar API error",
            error_type="HttpError",
            error_message="401 Unauthorized",
            user_id=user_id,
            calendar_id="primary")
```

## 🧪 Testing the Improvements

### **1. Run the Logging Test**
```bash
python test_improved_logging.py
```

This will demonstrate:
- Different log levels with colors and emojis
- Context grouping for user, trigger, and error information
- Google Calendar adapter logging in action

### **2. Start the Service with Improved Logging**
```bash
# Make sure .env has LOG_FORMAT=text
python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

### **3. Create a Trigger to See Logging**
```bash
curl -X POST "http://localhost:8000/api/v1/triggers/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: abc" \
  -d '{
    "user_id": "test_user",
    "workflow_id": "test_workflow",
    "trigger_type": "google_calendar",
    "trigger_name": "Test Trigger",
    "trigger_config": {
      "calendar_id": "primary",
      "use_polling": true,
      "poll_interval_seconds": 30
    },
    "event_types": ["created", "updated"]
  }'
```

## 📊 Example Output

### **Trigger Setup (Success)**
```
[14:25:06]     INFO GoogleCalendarAdapter    | 🔧 Setting up Google Calendar trigger
    👤 User: user_id=alice
    🎯 Trigger: trigger_id=abc-123 | calendar_id=primary | event_types=['created', 'updated'] | use_polling=True

[14:25:06]     INFO GoogleCalendarAdapter    | ✅ Google Calendar polling trigger setup successful
    👤 User: user_id=alice
    🎯 Trigger: trigger_id=abc-123 | poll_interval=30 | calendar_id=primary
```

### **Event Detection**
```
[14:25:30]     INFO GoogleCalendarAdapter    | 🔄 Checking for new events
    👤 User: user_id=alice
    🎯 Trigger: trigger_id=abc-123 | calendar_id=primary

[14:25:31]     INFO GoogleCalendarAdapter    | 📅 Found new calendar events
    👤 User: user_id=alice
    🎯 Trigger: trigger_id=abc-123 | event_count=2 | calendar_id=primary

[14:25:31]     INFO GoogleCalendarAdapter    | 🎯 TRIGGER EVENT FIRED
    👤 User: user_id=alice
    🎯 Trigger: trigger_id=abc-123 | detection_method=polling
    📋 Details: event_type=created | event_title=Team Meeting
```

### **Error Handling**
```
[14:25:06]    ERROR GoogleCalendarAdapter    | ❌ Failed to get Google Calendar credentials
    👤 User: user_id=bob
    🎯 Trigger: trigger_id=def-456
    ❌ Error: error_type=FileNotFoundError | error_message=No token file found
```

## 🔄 Switching Between Formats

### **Development (Readable)**
```env
LOG_FORMAT=text
LOG_LEVEL=DEBUG
```

### **Production (Machine-Readable)**
```env
LOG_FORMAT=json
LOG_LEVEL=INFO
```

### **Quick Switch for Testing**
```python
from src.utils.logger import setup_logging

# Readable format
setup_logging(log_level="DEBUG", log_format="text")

# JSON format
setup_logging(log_level="INFO", log_format="json")
```

## 🎯 Benefits

### **For Development**
- **Easy to scan** with colors and emojis
- **Clear context** with grouped information
- **Reduced noise** from third-party libraries
- **Visual hierarchy** with indentation
- **Quick problem identification** with error grouping

### **For Production**
- **Machine-readable** JSON format preserved
- **Structured data** for log aggregation
- **Correlation IDs** for request tracing
- **Sensitive data filtering** for security
- **Performance optimized** for high-volume logging

### **For Debugging**
- **User context** clearly visible
- **Trigger information** grouped together
- **Error details** prominently displayed
- **Event flow** easy to follow
- **Timestamps** in readable format

The logging system now provides the best of both worlds: readable logs for development and structured logs for production, with automatic context grouping and visual enhancements that make debugging much easier!
