#!/usr/bin/env python3
"""
Register Google Calendar webhook subscription for FastAPI endpoint
"""

import os
import json
import uuid
from datetime import datetime, timezone
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Configuration from your .env file
WEBHOOK_URL = (
    "https://99b5-103-173-221-201.ngrok-free.app/api/v1/webhooks/google-calendar"
)
CALENDAR_ID = "primary"  # Use 'primary' for the user's main calendar
WEBHOOK_TTL = 604800  # 7 days (maximum allowed by Google)


def load_credentials():
    """Load Google Calendar API credentials"""
    print("🔐 Loading Google Calendar credentials...")

    # Look for token file in common locations
    token_paths = ["token.json", "./token.json", "../token.json", "../../token.json"]

    token_file = None
    for path in token_paths:
        if os.path.exists(path):
            token_file = path
            print(f"   Found credentials: {path}")
            break

    if not token_file:
        print("❌ No token.json file found!")
        print("   Please ensure you have Google OAuth2 credentials.")
        print("   Searched paths:", token_paths)
        print("\n💡 To create credentials:")
        print("   1. Go to Google Cloud Console")
        print("   2. Enable Calendar API")
        print("   3. Create OAuth2 credentials")
        print("   4. Download and save as token.json")
        return None

    try:
        # Load credentials
        creds = Credentials.from_authorized_user_file(token_file)

        # Refresh if needed
        if not creds.valid:
            if creds.expired and creds.refresh_token:
                print("🔄 Refreshing expired credentials...")
                creds.refresh(Request())

                # Save refreshed credentials
                with open(token_file, "w") as f:
                    f.write(creds.to_json())
                print("✅ Credentials refreshed")
            else:
                print("❌ Credentials are invalid and cannot be refreshed")
                return None

        print("✅ Credentials loaded successfully")
        return creds

    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return None


def test_webhook_endpoint():
    """Test if the webhook endpoint is accessible"""
    print("🌐 Testing webhook endpoint accessibility...")

    try:
        import requests

        # Test the status endpoint first
        status_url = WEBHOOK_URL.replace("/google-calendar", "/status")
        response = requests.get(status_url, timeout=10)

        if response.status_code == 200:
            print("✅ Webhook endpoint is accessible")
            print(f"   Status: {response.json()}")
            return True
        else:
            print(f"❌ Webhook endpoint returned status: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Cannot reach webhook endpoint: {e}")
        print("\n💡 Make sure:")
        print(
            "   1. Your FastAPI server is running (python -m uvicorn src.main:app --host 0.0.0.0 --port 8000)"
        )
        print("   2. Your ngrok tunnel is active (ngrok http 8000)")
        print("   3. The ngrok URL matches the one in this script")
        return False


def register_webhook(service):
    """Register the webhook subscription with Google Calendar"""
    print("📝 Registering webhook subscription...")

    try:
        # Generate unique channel ID
        channel_id = f"fastapi-webhook-{str(uuid.uuid4())[:8]}"

        # Calculate expiration (7 days from now)
        expiration_timestamp = datetime.now(timezone.utc).timestamp() + WEBHOOK_TTL
        expiration_ms = int(expiration_timestamp * 1000)

        # Create the watch request
        watch_request = {
            "id": channel_id,
            "type": "web_hook",
            "address": WEBHOOK_URL,
            "expiration": str(expiration_ms),
        }

        print(f"   Channel ID: {channel_id}")
        print(f"   Webhook URL: {WEBHOOK_URL}")
        print(f"   Calendar ID: {CALENDAR_ID}")
        print(
            f"   Expires: {datetime.fromtimestamp(expiration_timestamp, timezone.utc)}"
        )

        # Execute the watch request
        print("\n🚀 Sending registration request to Google Calendar API...")
        result = (
            service.events().watch(calendarId=CALENDAR_ID, body=watch_request).execute()
        )

        print("\n🎉 SUCCESS! Webhook subscription registered!")
        print("=" * 50)
        print(f"Channel ID: {result.get('id')}")
        print(f"Resource ID: {result.get('resourceId')}")
        print(
            f"Expiration: {datetime.fromtimestamp(int(result.get('expiration', 0))/1000, timezone.utc)}"
        )

        # Save subscription info for later reference
        subscription_info = {
            "channel_id": result.get("id"),
            "resource_id": result.get("resourceId"),
            "webhook_url": WEBHOOK_URL,
            "calendar_id": CALENDAR_ID,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "expires_at": datetime.fromtimestamp(
                int(result.get("expiration", 0)) / 1000, timezone.utc
            ).isoformat(),
        }

        with open("webhook_subscription.json", "w") as f:
            json.dump(subscription_info, f, indent=2)

        print(f"\n💾 Subscription info saved to: webhook_subscription.json")

        return result

    except HttpError as e:
        print(f"\n❌ Google Calendar API Error: {e}")

        if e.resp.status == 400:
            print("\n💡 Possible causes:")
            print("   - Webhook URL is not accessible from the internet")
            print("   - URL is not HTTPS")
            print("   - Google cannot reach your ngrok tunnel")
            print("   - Invalid request format")

        elif e.resp.status == 401:
            print("\n💡 Authentication error:")
            print("   - Check your OAuth2 credentials")
            print("   - Ensure token.json is valid")

        elif e.resp.status == 403:
            print("\n💡 Permission error:")
            print("   - Check OAuth2 scopes include calendar access")
            print("   - Verify API is enabled in Google Cloud Console")

        return None

    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return None


def main():
    print("🚀 Google Calendar Webhook Registration Tool")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print(f"Target URL: {WEBHOOK_URL}")
    print()

    # Step 1: Test webhook endpoint
    if not test_webhook_endpoint():
        print("\n❌ CRITICAL: Cannot reach webhook endpoint!")
        print("Please fix the endpoint accessibility before proceeding.")
        return False

    print()

    # Step 2: Load credentials
    creds = load_credentials()
    if not creds:
        print("\n❌ CRITICAL: Cannot load Google Calendar credentials!")
        return False

    print()

    # Step 3: Create Calendar service
    try:
        service = build("calendar", "v3", credentials=creds)
        print("✅ Google Calendar service created")
    except Exception as e:
        print(f"❌ Error creating Calendar service: {e}")
        return False

    print()

    # Step 4: Register webhook
    result = register_webhook(service)

    if result:
        print("\n🎉 WEBHOOK REGISTRATION COMPLETE!")
        print("\n📋 Next steps:")
        print("1. Create or modify a calendar event to test the webhook")
        print("2. Check your FastAPI server logs for incoming notifications")
        print("3. Monitor ngrok traffic at: http://localhost:4040")
        print("4. The webhook will expire in 7 days and needs to be renewed")

        print("\n🧪 Test the webhook:")
        print("   - Go to Google Calendar")
        print("   - Create a new event")
        print("   - Check your FastAPI logs for webhook notifications")

        return True
    else:
        print("\n❌ WEBHOOK REGISTRATION FAILED!")
        print("Please check the error messages above and try again.")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ All done! Your Google Calendar webhook is now active.")
    else:
        print("\n❌ Setup incomplete. Please resolve the issues and try again.")
