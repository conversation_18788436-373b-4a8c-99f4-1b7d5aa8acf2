#!/usr/bin/env python3
"""
<PERSON>ript to automatically update the Google Calendar webhook URL in .env file.
This script fetches the current ngrok URL and updates the configuration.
"""

import json
import re
import sys
from pathlib import Path
from urllib.request import urlopen
from urllib.error import URLError


def get_ngrok_url():
    """Get the current ngrok HTTPS URL."""
    try:
        with urlopen("http://localhost:4040/api/tunnels") as response:
            data = json.loads(response.read().decode())
            
        tunnels = data.get("tunnels", [])
        for tunnel in tunnels:
            if tunnel.get("proto") == "https":
                return tunnel["public_url"]
                
        return None
    except URLError:
        return None


def update_env_file(webhook_url):
    """Update the .env file with the new webhook URL."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Read current content
    content = env_file.read_text()
    
    # Pattern to match the Google Calendar webhook URL line
    pattern = r"GOOGLE_CALENDAR_WEBHOOK_URL=.*"
    replacement = f"GOOGLE_CALENDAR_WEBHOOK_URL={webhook_url}"
    
    # Replace the line
    new_content = re.sub(pattern, replacement, content)
    
    if new_content == content:
        print("❌ Could not find GOOGLE_CALENDAR_WEBHOOK_URL in .env file")
        return False
    
    # Write back to file
    env_file.write_text(new_content)
    return True


def main():
    """Main function."""
    print("🔧 Updating Google Calendar webhook URL...")
    
    # Get ngrok URL
    ngrok_url = get_ngrok_url()
    if not ngrok_url:
        print("❌ Could not get ngrok URL. Make sure ngrok is running on port 4040.")
        print("   Start ngrok with: ngrok http 8000")
        sys.exit(1)
    
    # Construct webhook URL
    webhook_url = f"{ngrok_url}/api/v1/webhooks/google-calendar"
    
    # Update .env file
    if update_env_file(webhook_url):
        print(f"✅ Updated .env file with webhook URL:")
        print(f"   {webhook_url}")
        print("")
        print("🔄 Please restart your trigger service to pick up the new configuration:")
        print("   python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000")
    else:
        print("❌ Failed to update .env file")
        sys.exit(1)


if __name__ == "__main__":
    main()
