#!/bin/bash

# Setup script for ngrok tunnel for Google Calendar webhook development
# This script helps set up a secure HTTPS tunnel for local development

set -e

echo "🚀 Setting up ngrok tunnel for Google Calendar webhook development"
echo ""

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed. Please install it first:"
    echo ""
    echo "On macOS with Homebrew:"
    echo "  brew install ngrok"
    echo ""
    echo "Or download from: https://ngrok.com/download"
    echo ""
    exit 1
fi

# Default port
PORT=${1:-8000}

echo "📡 Starting ngrok tunnel on port $PORT..."
echo ""
echo "This will:"
echo "  1. Create a secure HTTPS tunnel to your local server"
echo "  2. Provide you with a public HTTPS URL"
echo "  3. Allow Google Calendar to send webhooks to your local development server"
echo ""

# Start ngrok in background and capture the URL
echo "Starting ngrok..."
ngrok http $PORT --log=stdout > /tmp/ngrok.log &
NGROK_PID=$!

# Wait a moment for ngrok to start
sleep 3

# Get the public URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    tunnels = data.get('tunnels', [])
    for tunnel in tunnels:
        if tunnel.get('proto') == 'https':
            print(tunnel['public_url'])
            break
except:
    pass
")

if [ -z "$NGROK_URL" ]; then
    echo "❌ Failed to get ngrok URL. Please check if ngrok started correctly."
    kill $NGROK_PID 2>/dev/null || true
    exit 1
fi

WEBHOOK_URL="$NGROK_URL/api/v1/webhooks/google-calendar"

echo ""
echo "✅ ngrok tunnel is running!"
echo ""
echo "📋 Configuration:"
echo "  Local server:    http://localhost:$PORT"
echo "  Public HTTPS:    $NGROK_URL"
echo "  Webhook URL:     $WEBHOOK_URL"
echo ""
echo "🔧 Next steps:"
echo "  1. Update your .env file with:"
echo "     GOOGLE_CALENDAR_WEBHOOK_URL=$WEBHOOK_URL"
echo ""
echo "  2. Restart your trigger service to pick up the new configuration"
echo ""
echo "  3. Test your Google Calendar trigger setup"
echo ""
echo "📊 Monitor ngrok traffic at: http://localhost:4040"
echo ""
echo "⚠️  Important notes:"
echo "  - Keep this terminal open while testing"
echo "  - The ngrok URL changes each time you restart (unless you have a paid plan)"
echo "  - Update the .env file each time you get a new ngrok URL"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping ngrok tunnel..."
    kill $NGROK_PID 2>/dev/null || true
    echo "✅ Cleanup complete"
}

# Set trap to cleanup on script exit
trap cleanup EXIT

echo "Press Ctrl+C to stop the tunnel..."
echo ""

# Keep the script running
wait $NGROK_PID
