#!/usr/bin/env python3
"""
Test script to verify configuration loading
"""

import os
from src.utils.config import get_settings


def test_config():
    print("=== Environment Variables ===")
    print(
        f"GOOGLE_CALENDAR_WEBHOOK_URL from os.environ: {os.environ.get('GOOGLE_CALENDAR_WEBHOOK_URL', 'NOT SET')}"
    )

    print("\n=== Pydantic Settings ===")
    settings = get_settings()
    print(
        f"google_calendar_webhook_url from settings: {settings.google_calendar_webhook_url}"
    )

    print(
        f"\nHTTPS check: {settings.google_calendar_webhook_url.startswith('https://')}"
    )

    print("\n=== All Settings ===")
    print(f"Debug: {settings.debug}")
    print(f"Host: {settings.host}")
    print(f"Port: {settings.port}")


if __name__ == "__main__":
    test_config()
