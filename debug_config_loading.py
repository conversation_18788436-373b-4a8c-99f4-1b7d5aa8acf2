#!/usr/bin/env python3
"""
Debug script to check configuration loading
"""

import os
import sys

sys.path.append(".")


def test_env_loading():
    print("=== Environment Variable Check ===")
    webhook_url = os.environ.get("GOOGLE_CALENDAR_WEBHOOK_URL")
    print(f"GOOGLE_CALENDAR_WEBHOOK_URL from os.environ: {webhook_url}")

    print("\n=== .env File Check ===")
    try:
        with open(".env", "r") as f:
            lines = f.readlines()
            for line in lines:
                if "GOOGLE_CALENDAR_WEBHOOK_URL" in line:
                    print(f"Found in .env: {line.strip()}")
    except Exception as e:
        print(f"Error reading .env: {e}")

    print("\n=== Pydantic Settings Check ===")
    try:
        from src.utils.config import get_settings

        settings = get_settings()
        print(f"Settings type: {type(settings)}")
        print(
            f"Has google_calendar_webhook_url: {hasattr(settings, 'google_calendar_webhook_url')}"
        )
        if hasattr(settings, "google_calendar_webhook_url"):
            print(f"Webhook URL from settings: {settings.google_calendar_webhook_url}")

        # Check all attributes
        attrs = [
            attr
            for attr in dir(settings)
            if not attr.startswith("_") and "google" in attr.lower()
        ]
        print(f"Google-related attributes: {attrs}")

    except Exception as e:
        print(f"Error loading settings: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test_env_loading()
