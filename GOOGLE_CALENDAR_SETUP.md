# Google Calendar Integration Setup

This document explains how to set up Google Calendar integration for the trigger service while the Auth service is being developed.

## Current Status

🚧 **Temporary Setup**: The trigger service is currently configured to load Google Calendar credentials from a local `token.json` file instead of the Auth service.

## Prerequisites

1. Google Cloud Project with Calendar API enabled
2. OAuth2 credentials (Client ID and Client Secret)
3. Valid Google Calendar access token and refresh token

## Setup Steps

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Calendar API
4. Create OAuth2 credentials (Web application type)

### 2. Get OAuth2 Tokens

You can obtain tokens using Google's OAuth2 Playground or by implementing a simple OAuth2 flow.

**Using OAuth2 Playground:**
1. Go to [OAuth2 Playground](https://developers.google.com/oauthplayground/)
2. Click the gear icon and check "Use your own OAuth credentials"
3. Enter your Client ID and Client Secret
4. In Step 1, select "Calendar API v3" and the scope: `https://www.googleapis.com/auth/calendar`
5. Click "Authorize APIs" and complete the OAuth flow
6. In Step 2, click "Exchange authorization code for tokens"
7. Copy the access_token and refresh_token

### 3. Create token.json File

Create a `token.json` file in the project root directory with the following format:

```json
{
  "token": "your_access_token_here",
  "refresh_token": "your_refresh_token_here", 
  "token_uri": "https://oauth2.googleapis.com/token",
  "client_id": "your_client_id_here.apps.googleusercontent.com",
  "client_secret": "your_client_secret_here",
  "scopes": ["https://www.googleapis.com/auth/calendar"]
}
```

**Important**: 
- Replace all placeholder values with your actual credentials
- Keep this file secure and never commit it to version control
- The file is already added to `.gitignore`

### 4. Test the Integration

1. Start the trigger service:
   ```bash
   poetry run python -m src.main
   ```

2. Create a test trigger:
   ```bash
   curl -X POST \
     -H "Authorization: Bearer abc" \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "test-user",
       "workflow_id": "test-workflow", 
       "trigger_type": "google_calendar",
       "trigger_name": "Test Calendar Trigger",
       "trigger_config": {"calendar_id": "primary"},
       "event_types": ["created"]
     }' \
     http://localhost:8000/api/v1/triggers/
   ```

## Configuration Options

The `trigger_config` supports the following options:

- `calendar_id` (required): The calendar ID to monitor (use "primary" for the user's primary calendar)
- `webhook_ttl` (optional): Webhook subscription TTL in seconds (default: 3600)
- `event_filters` (optional): Additional event filtering options

## Webhook Configuration

For the webhook functionality to work completely, you'll also need to:

1. Set up a public webhook endpoint (using ngrok for development)
2. Configure the `GOOGLE_CALENDAR_WEBHOOK_URL` environment variable
3. Implement webhook verification and processing

## Migration to Auth Service

Once the Auth service is ready, the following changes will be made:

1. Remove the local `token.json` file loading
2. Uncomment the `AuthClient` initialization
3. Update the `_get_user_credentials` method to use the Auth service
4. Implement proper user OAuth2 flow through the Auth service

## Troubleshooting

### Common Issues

1. **"No token.json file found"**: Ensure the file exists in the project root
2. **"Invalid JSON"**: Check the JSON syntax in your token.json file
3. **"Token expired"**: The service will automatically refresh tokens if a refresh_token is provided
4. **"Calendar API not enabled"**: Enable the Calendar API in Google Cloud Console
5. **"Invalid credentials"**: Verify your Client ID and Client Secret are correct

### Logs

Check the application logs for detailed error messages:
```bash
poetry run python -m src.main
```

The service will log credential loading attempts and any errors encountered.
