# Google Calendar Polling Integration Guide

Your Google Calendar POC has been successfully integrated into the trigger service! This guide explains how the integration works and how to use it.

## What Was Integrated

Your POC (`google_calender_poc.py`) provided excellent polling logic that has been integrated into the Google Calendar adapter with the following enhancements:

### From Your POC ✅
- **Authentication handling** with token refresh
- **State management** to track last processed events  
- **Event filtering by creation time** (more accurate than `timeMin`)
- **Polling loop** with configurable intervals
- **Error handling** and recovery
- **Event detection logic**

### Enhanced for Trigger Service ✅
- **Async/await support** for non-blocking operations
- **Integration with trigger event system**
- **Webhook + polling hybrid approach**
- **Multiple trigger support** (each trigger has its own polling task)
- **Proper cleanup** and resource management
- **Structured logging** and monitoring

## How It Works

### 1. Hybrid Approach
The adapter now supports both webhook and polling modes:

```python
# Webhook mode (preferred, requires HTTPS)
trigger_config = {
    "calendar_id": "primary",
    "use_polling": False  # Default
}

# Polling mode (your POC approach)
trigger_config = {
    "calendar_id": "primary", 
    "use_polling": True,
    "poll_interval_seconds": 60  # Based on your POC's POLL_INTERVAL_SECONDS
}

# Automatic fallback (tries webhook, falls back to polling)
trigger_config = {
    "calendar_id": "primary"
    # Will try webhook first, use polling if webhook fails
}
```

### 2. Your POC Logic Preserved
The core logic from your POC is preserved in these methods:

- `_get_newly_created_events()` - Your event filtering logic
- `_polling_loop()` - Your main polling loop structure  
- `_setup_polling_trigger()` - State management from your POC

### 3. Event Processing
When new events are detected (using your POC logic), they are converted to standardized trigger events and processed by the workflow system.

## Usage Examples

### 1. Create a Polling Trigger

```bash
curl -X POST "http://localhost:8000/api/v1/triggers/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: abc" \
  -d '{
    "user_id": "user123",
    "workflow_id": "workflow456", 
    "trigger_type": "google_calendar",
    "trigger_name": "My Polling Calendar Trigger",
    "trigger_config": {
      "calendar_id": "primary",
      "use_polling": true,
      "poll_interval_seconds": 30
    },
    "event_types": ["created", "updated"]
  }'
```

### 2. Create a Webhook Trigger with Polling Fallback

```bash
curl -X POST "http://localhost:8000/api/v1/triggers/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: abc" \
  -d '{
    "user_id": "user123",
    "workflow_id": "workflow456",
    "trigger_type": "google_calendar", 
    "trigger_name": "My Hybrid Calendar Trigger",
    "trigger_config": {
      "calendar_id": "primary"
    },
    "event_types": ["created", "updated"]
  }'
```

## Configuration Options

Based on your POC, the following configuration options are supported:

| Option | Description | Default | Your POC Equivalent |
|--------|-------------|---------|-------------------|
| `calendar_id` | Calendar to monitor | `"primary"` | Hard-coded "primary" |
| `use_polling` | Force polling mode | `false` | Always true in POC |
| `poll_interval_seconds` | Polling frequency | `60` | `POLL_INTERVAL_SECONDS = 15` |
| `webhook_ttl` | Webhook subscription TTL | `3600` | N/A (polling only) |

## Testing the Integration

### 1. Run the Test Script

```bash
python test_polling_integration.py
```

This will:
- Set up a polling trigger using your POC logic
- Monitor for new calendar events
- Show detected events in the logs
- Clean up after testing

### 2. Manual Testing

1. **Start the trigger service**:
   ```bash
   python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Create a polling trigger** (see examples above)

3. **Create a calendar event** in Google Calendar

4. **Watch the logs** for event detection:
   ```
   TRIGGER EVENT: created - Your New Event Title
   ```

## Advantages of the Integration

### Compared to Your POC
- ✅ **Multiple triggers**: Support many users/calendars simultaneously
- ✅ **Non-blocking**: Async operations don't block the service
- ✅ **Integrated**: Works with the full workflow automation platform
- ✅ **Hybrid approach**: Webhooks when possible, polling as fallback
- ✅ **Better error handling**: Structured logging and recovery
- ✅ **Resource management**: Proper cleanup of polling tasks

### Compared to Webhook-Only
- ✅ **Always works**: No HTTPS requirement for development
- ✅ **Reliable**: Not dependent on external webhook delivery
- ✅ **Immediate**: Can detect events faster than webhook delays
- ✅ **Debuggable**: Easy to see what's happening in logs

## Migration from Your POC

If you want to migrate from your standalone POC to the integrated version:

### 1. Stop Your POC Script
```bash
# Stop your google_calender_poc.py if it's running
```

### 2. Create Integrated Triggers
Use the API calls shown above to create triggers for each calendar you were monitoring.

### 3. Monitor Through Service
Instead of console output, monitor through:
- Service logs: `tail -f logs/trigger-service.log`
- API endpoints: `GET /api/v1/triggers/`
- Health checks: `GET /api/v1/health`

## Troubleshooting

### Common Issues

1. **"No token.json file found"**
   - Copy your `token.json` from the POC directory
   - Ensure it's in the trigger service root directory

2. **"Polling not starting"**
   - Check that `use_polling: true` is set
   - Verify credentials are valid
   - Check service logs for errors

3. **"Events not detected"**
   - Verify the calendar has new events after trigger creation
   - Check the `poll_interval_seconds` setting
   - Monitor debug logs for polling activity

### Debug Mode

Enable debug logging to see detailed polling activity:

```env
LOG_LEVEL=DEBUG
```

This will show:
- Polling loop iterations
- Event detection details
- State updates
- Error details

## Next Steps

1. **Test the integration** with your existing Google Calendar setup
2. **Adjust polling intervals** based on your needs
3. **Set up HTTPS** for webhook mode when ready for production
4. **Monitor performance** and adjust as needed
5. **Integrate with your workflows** to trigger actions on calendar events

Your POC provided an excellent foundation - the integration preserves all the good parts while making it production-ready and scalable!
