# Google Calendar Webhook Setup Guide

This guide explains how to set up Google Calendar webhooks for local development and production environments.

## Problem: HTTPS Requirement

Google Calendar requires webhook URLs to use HTTPS for security reasons. The error you might see is:

```
WebHook callback must be HTTPS: http://localhost:8000/api/v1/webhooks/google-calendar
```

## Solution for Development

For local development, you need to create a secure HTTPS tunnel to your local server using ngrok.

### Method 1: Automated Setup (Recommended)

1. **Install ngrok** (if not already installed):
   ```bash
   # On macOS with Homebrew
   brew install ngrok
   
   # Or download from https://ngrok.com/download
   ```

2. **Use the automated setup script**:
   ```bash
   ./scripts/setup-ngrok.sh
   ```
   
   This script will:
   - Start an ngrok tunnel on port 8000
   - Display the public HTTPS URL
   - Provide instructions for updating your configuration

3. **Update your .env file** with the ngrok URL:
   ```bash
   python scripts/update-webhook-url.py
   ```
   
   Or manually update `.env`:
   ```env
   GOOGLE_CALENDAR_WEBHOOK_URL=https://abc123.ngrok.io/api/v1/webhooks/google-calendar
   ```

4. **Restart your trigger service**:
   ```bash
   python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Method 2: Manual Setup

1. **Start ngrok tunnel**:
   ```bash
   ngrok http 8000
   ```

2. **Copy the HTTPS URL** from the ngrok output (e.g., `https://abc123.ngrok.io`)

3. **Update your .env file**:
   ```env
   GOOGLE_CALENDAR_WEBHOOK_URL=https://abc123.ngrok.io/api/v1/webhooks/google-calendar
   ```

4. **Restart your trigger service**

## Testing the Setup

1. **Create a Google Calendar trigger**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/triggers/" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: abc" \
     -d '{
       "user_id": "user123",
       "workflow_id": "workflow456",
       "trigger_type": "google_calendar",
       "trigger_name": "My Calendar Trigger",
       "trigger_config": {
         "calendar_id": "primary"
       },
       "event_types": ["created", "updated"]
     }'
   ```

2. **Check the logs** for successful webhook subscription creation

3. **Monitor webhook traffic** at http://localhost:4040 (ngrok web interface)

## Important Notes

### Development
- **Keep ngrok running**: The tunnel must stay active while testing
- **URL changes**: Free ngrok URLs change each restart (paid plans get static URLs)
- **Update configuration**: Remember to update the .env file with new ngrok URLs
- **Monitor traffic**: Use http://localhost:4040 to see incoming webhook requests

### Production
- Use a proper HTTPS domain for your production webhook URL
- Configure SSL certificates properly
- Consider using a reverse proxy (nginx, CloudFlare, etc.)

## Troubleshooting

### Common Issues

1. **"WebHook callback must be HTTPS"**
   - Solution: Ensure your webhook URL starts with `https://`
   - For development: Use ngrok to create HTTPS tunnel

2. **"ngrok not found"**
   - Solution: Install ngrok first
   - macOS: `brew install ngrok`
   - Other platforms: Download from https://ngrok.com/download

3. **"Could not get ngrok URL"**
   - Solution: Make sure ngrok is running on port 4040
   - Check: `curl http://localhost:4040/api/tunnels`

4. **Webhook not receiving events**
   - Check ngrok is still running
   - Verify the webhook URL in your .env file
   - Check Google Calendar subscription is active
   - Monitor ngrok traffic at http://localhost:4040

### Logs to Check

- **Trigger service logs**: Look for webhook subscription creation messages
- **ngrok logs**: Check for incoming requests
- **Google Calendar API errors**: Check for authentication or permission issues

## Scripts Reference

- `scripts/setup-ngrok.sh`: Automated ngrok setup with instructions
- `scripts/update-webhook-url.py`: Automatically update .env with current ngrok URL

## Environment Variables

```env
# Required for Google Calendar webhooks
GOOGLE_CALENDAR_WEBHOOK_URL=https://your-domain.com/api/v1/webhooks/google-calendar

# Development example with ngrok
GOOGLE_CALENDAR_WEBHOOK_URL=https://abc123.ngrok.io/api/v1/webhooks/google-calendar
```

## Next Steps

After setting up the webhook URL:

1. Test Google Calendar trigger creation
2. Create/update calendar events to test webhook delivery
3. Monitor logs and ngrok traffic
4. Implement proper error handling and retry logic
5. Set up production HTTPS domain when ready to deploy
