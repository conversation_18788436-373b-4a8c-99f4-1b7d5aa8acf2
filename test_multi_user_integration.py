#!/usr/bin/env python3
"""
Test script to demonstrate the multi-user Google Calendar integration.
Based on your webhook implementation for handling multiple users.
"""

import asyncio
import json
from datetime import datetime
from uuid import uuid4

# Add the src directory to the path so we can import our modules
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from adapters.google_calendar import GoogleCalendarAdapter
from adapters.base import TriggerConfiguration


async def test_multi_user_setup():
    """Test setting up triggers for multiple users."""
    print("🧪 Testing Multi-User Google Calendar Integration")
    print("=" * 60)
    
    adapter = GoogleCalendarAdapter()
    
    # Create triggers for multiple users
    users = [
        {"user_id": "user_alice", "calendar_id": "primary"},
        {"user_id": "user_bob", "calendar_id": "primary"},
        {"user_id": "user_charlie", "calendar_id": "primary"},
    ]
    
    trigger_configs = []
    
    for user in users:
        trigger_config = TriggerConfiguration(
            trigger_id=uuid4(),
            user_id=user["user_id"],
            workflow_id=f"workflow_{user['user_id']}",
            trigger_type="google_calendar",
            trigger_name=f"Calendar Trigger for {user['user_id']}",
            config={
                "calendar_id": user["calendar_id"],
                "use_polling": True,  # Use polling for testing
                "poll_interval_seconds": 45,
            },
            event_types=["created", "updated"],
            is_active=True,
        )
        trigger_configs.append(trigger_config)
    
    print(f"📋 Setting up triggers for {len(users)} users:")
    for config in trigger_configs:
        print(f"   - {config.user_id}: {config.trigger_id}")
    print()
    
    # Set up triggers for all users
    successful_setups = []
    for config in trigger_configs:
        print(f"🚀 Setting up trigger for {config.user_id}...")
        success = await adapter.setup_trigger(config)
        
        if success:
            successful_setups.append(config)
            print(f"   ✅ Success for {config.user_id}")
        else:
            print(f"   ❌ Failed for {config.user_id}")
    
    print(f"\n📊 Results: {len(successful_setups)}/{len(trigger_configs)} triggers set up successfully")
    
    if successful_setups:
        print("\n⏱️  Running multi-user polling for 1 minute...")
        print("   Create calendar events for different users to see them detected!")
        
        try:
            await asyncio.sleep(60)
        except KeyboardInterrupt:
            print("\n⏹️  Stopping early due to user interrupt")
        
        # Clean up all triggers
        print("\n🧹 Cleaning up triggers...")
        for config in successful_setups:
            cleanup_success = await adapter.remove_trigger(config.trigger_id)
            print(f"   {config.user_id}: {'✅' if cleanup_success else '❌'}")
    
    return len(successful_setups)


async def test_webhook_channel_management():
    """Test webhook channel management functionality."""
    print("\n🔧 Testing Webhook Channel Management")
    print("=" * 60)
    
    adapter = GoogleCalendarAdapter()
    
    # Create a few webhook triggers (will likely fail but create channel entries)
    webhook_configs = []
    for i in range(3):
        config = TriggerConfiguration(
            trigger_id=uuid4(),
            user_id=f"webhook_user_{i}",
            workflow_id=f"webhook_workflow_{i}",
            trigger_type="google_calendar",
            trigger_name=f"Webhook Trigger {i}",
            config={
                "calendar_id": "primary",
                "use_polling": False,  # Try webhook first
                "webhook_ttl": 3600,
            },
            event_types=["created", "updated"],
            is_active=True,
        )
        webhook_configs.append(config)
    
    print("📡 Attempting webhook setups (may fallback to polling)...")
    for config in webhook_configs:
        success = await adapter.setup_trigger(config)
        print(f"   {config.user_id}: {'✅' if success else '❌'}")
    
    # Test webhook status
    print("\n📊 Webhook Channel Status:")
    status = await adapter.get_webhook_status()
    
    if "error" in status:
        print(f"   Error getting status: {status['error']}")
    else:
        print(f"   Total channels: {status['total_channels']}")
        print(f"   Expired channels: {status['expired_channels']}")
        
        for channel in status["active_channels"]:
            print(f"   - Channel {channel['channel_id'][:8]}...")
            print(f"     User: {channel['user_id']}")
            print(f"     Trigger: {channel['trigger_id'][:8]}...")
            print(f"     Expires: {channel['expires_at']}")
            print(f"     Expired: {channel['is_expired']}")
    
    # Test cleanup
    print("\n🧹 Testing channel cleanup...")
    cleaned_count = await adapter.cleanup_expired_channels()
    print(f"   Cleaned up {cleaned_count} expired channels")
    
    # Clean up remaining triggers
    for config in webhook_configs:
        await adapter.remove_trigger(config.trigger_id)


async def test_credential_caching():
    """Test multi-user credential caching."""
    print("\n🔐 Testing Multi-User Credential Caching")
    print("=" * 60)
    
    adapter = GoogleCalendarAdapter()
    
    # Test credential loading for multiple users
    test_users = ["user_test_1", "user_test_2", "user_test_3"]
    
    print("📝 Testing credential loading for multiple users...")
    for user_id in test_users:
        print(f"   Loading credentials for {user_id}...")
        credentials = await adapter._get_user_credentials(user_id)
        
        if credentials:
            print(f"   ✅ Credentials loaded for {user_id}")
            print(f"      Valid: {credentials.valid}")
            print(f"      Expired: {credentials.expired}")
        else:
            print(f"   ❌ No credentials found for {user_id}")
    
    # Test credential caching (second load should be faster)
    print("\n🚀 Testing credential caching (second load)...")
    for user_id in test_users:
        credentials = await adapter._get_user_credentials(user_id)
        status = "✅ Cached" if credentials else "❌ Not found"
        print(f"   {user_id}: {status}")


def create_sample_token_files():
    """Create sample token files for testing multi-user setup."""
    print("\n📁 Creating sample token file structure...")
    
    # Create tokens directory
    tokens_dir = Path("tokens")
    tokens_dir.mkdir(exist_ok=True)
    
    # Check if main token.json exists
    if Path("token.json").exists():
        print("   ✅ Found main token.json file")
        
        # Copy to user-specific files for testing
        try:
            with open("token.json", "r") as f:
                token_data = json.load(f)
            
            for user_id in ["user_alice", "user_bob", "user_charlie"]:
                user_token_file = tokens_dir / f"token_{user_id}.json"
                with open(user_token_file, "w") as f:
                    json.dump(token_data, f, indent=2)
                print(f"   📄 Created {user_token_file}")
                
        except Exception as e:
            print(f"   ❌ Error creating user token files: {e}")
    else:
        print("   ⚠️  No main token.json found")
        print("      Please create a token.json file with Google Calendar credentials")


async def main():
    """Main test function."""
    print("🔧 Multi-User Google Calendar Integration Test")
    print("Based on your webhook implementation: google_calender_webhook.py")
    print()
    
    try:
        # Create sample token files for testing
        create_sample_token_files()
        
        # Test multi-user setup
        successful_users = await test_multi_user_setup()
        
        # Test webhook channel management
        await test_webhook_channel_management()
        
        # Test credential caching
        await test_credential_caching()
        
        print("\n✅ All multi-user tests completed!")
        print("\n📝 Summary of Multi-User Features:")
        print("   ✅ Multi-user credential management with caching")
        print("   ✅ Per-user webhook channel tracking")
        print("   ✅ User-specific token file support")
        print("   ✅ Concurrent polling for multiple users")
        print("   ✅ Channel lifecycle management")
        print("   ✅ Webhook signature verification")
        print("   ✅ Automatic cleanup of expired channels")
        
        if successful_users > 0:
            print(f"\n🎉 Successfully tested with {successful_users} users!")
        else:
            print("\n⚠️  No users were successfully set up")
            print("   Make sure you have valid Google Calendar credentials")
        
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
