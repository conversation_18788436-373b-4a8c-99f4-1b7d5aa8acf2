#!/usr/bin/env python3
"""
Test script to demonstrate the integrated Google Calendar polling functionality.
This script shows how your POC has been integrated into the trigger service.
"""

import asyncio
import json
from datetime import datetime
from uuid import uuid4

# Add the src directory to the path so we can import our modules
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from adapters.google_calendar import GoogleCalendarAdapter
from adapters.base import TriggerConfiguration


async def test_polling_integration():
    """Test the integrated polling functionality."""
    print("🧪 Testing Google Calendar Polling Integration")
    print("=" * 50)
    
    # Create adapter instance
    adapter = GoogleCalendarAdapter()
    
    # Create a test trigger configuration with polling enabled
    trigger_config = TriggerConfiguration(
        trigger_id=uuid4(),
        user_id="test_user_123",
        workflow_id="test_workflow_456",
        trigger_type="google_calendar",
        trigger_name="Test Polling Trigger",
        config={
            "calendar_id": "primary",
            "use_polling": True,  # Force polling mode
            "poll_interval_seconds": 30,  # Poll every 30 seconds for testing
        },
        event_types=["created", "updated"],
        is_active=True,
    )
    
    print(f"📋 Trigger Configuration:")
    print(f"   ID: {trigger_config.trigger_id}")
    print(f"   User: {trigger_config.user_id}")
    print(f"   Calendar: {trigger_config.config['calendar_id']}")
    print(f"   Poll Interval: {trigger_config.config['poll_interval_seconds']}s")
    print(f"   Event Types: {trigger_config.event_types}")
    print()
    
    # Test configuration validation
    print("🔍 Validating configuration...")
    is_valid = await adapter.validate_config(trigger_config.config)
    print(f"   Configuration valid: {is_valid}")
    
    if not is_valid:
        print("❌ Configuration validation failed")
        return
    
    print()
    
    # Set up the trigger (this will use polling since use_polling=True)
    print("🚀 Setting up trigger...")
    success = await adapter.setup_trigger(trigger_config)
    
    if success:
        print("✅ Trigger setup successful!")
        print(f"   Polling mode activated for trigger {trigger_config.trigger_id}")
        print()
        
        # Let it run for a bit to demonstrate polling
        print("⏱️  Running polling for 2 minutes (create some calendar events to test)...")
        print("   Check your Google Calendar and create a new event to see it detected!")
        print("   Press Ctrl+C to stop early")
        print()
        
        try:
            # Wait for 2 minutes to let polling work
            await asyncio.sleep(120)
        except KeyboardInterrupt:
            print("\n⏹️  Stopping early due to user interrupt")
        
        # Clean up
        print("\n🧹 Cleaning up...")
        cleanup_success = await adapter.remove_trigger(trigger_config.trigger_id)
        print(f"   Cleanup successful: {cleanup_success}")
        
    else:
        print("❌ Trigger setup failed!")
        print("   Make sure you have:")
        print("   1. A valid token.json file with Google Calendar credentials")
        print("   2. Proper Google Calendar API access")
        print("   3. Internet connectivity")


async def test_webhook_fallback():
    """Test webhook setup with fallback to polling."""
    print("\n🔄 Testing Webhook with Polling Fallback")
    print("=" * 50)
    
    adapter = GoogleCalendarAdapter()
    
    # Create trigger config without forcing polling (will try webhook first)
    trigger_config = TriggerConfiguration(
        trigger_id=uuid4(),
        user_id="test_user_456",
        workflow_id="test_workflow_789",
        trigger_type="google_calendar",
        trigger_name="Test Webhook Fallback Trigger",
        config={
            "calendar_id": "primary",
            "poll_interval_seconds": 60,  # Fallback polling interval
        },
        event_types=["created", "updated"],
        is_active=True,
    )
    
    print(f"📋 Testing webhook setup (will likely fail and fallback to polling)")
    print(f"   Trigger ID: {trigger_config.trigger_id}")
    print()
    
    # This should try webhook first, fail (due to HTTP URL), then fallback to polling
    success = await adapter.setup_trigger(trigger_config)
    
    if success:
        print("✅ Trigger setup successful (likely using polling fallback)!")
        
        # Clean up after a short time
        await asyncio.sleep(5)
        cleanup_success = await adapter.remove_trigger(trigger_config.trigger_id)
        print(f"🧹 Cleanup successful: {cleanup_success}")
    else:
        print("❌ Trigger setup failed completely")


def main():
    """Main function to run the tests."""
    print("🔧 Google Calendar Polling Integration Test")
    print("Based on your POC: google_calender_poc.py")
    print()
    
    try:
        # Run the polling integration test
        asyncio.run(test_polling_integration())
        
        # Run the webhook fallback test
        asyncio.run(test_webhook_fallback())
        
        print("\n✅ All tests completed!")
        print("\n📝 Summary:")
        print("   - Your POC polling logic has been integrated into the trigger service")
        print("   - The adapter now supports both webhook and polling modes")
        print("   - Polling is used as a fallback when webhooks fail")
        print("   - The same authentication and event detection logic from your POC is preserved")
        
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
