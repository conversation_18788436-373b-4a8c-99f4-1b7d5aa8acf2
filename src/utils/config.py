"""
Configuration management for the Trigger Service.

This module provides centralized configuration management using Pydantic
BaseSettings for environment variable validation and type conversion.
"""

import os
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.

    This class uses Pydantic BaseSettings to automatically load and validate
    configuration from environment variables with proper type conversion.
    """

    # Application Configuration
    debug: bool = Field(default=False, description="Enable debug mode")
    host: str = Field(default="0.0.0.0", description="Host to bind the server")
    port: int = Field(default=8000, description="Port to bind the server")
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Logging format (json or text)")

    # CORS Configuration
    cors_origins: list[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:8080",
            "https://99b5-103-173-221-201.ngrok-free.app",
        ],
        alias="CORS_ORIGINS",
        description="Allowed CORS origins",
    )

    # Database Configuration
    database_url: str = Field(
        ..., alias="DATABASE_URL", description="PostgreSQL database URL"
    )

    # Auth Service Configuration
    auth_service_url: str = Field(
        ..., alias="AUTH_SERVICE_URL", description="URL of the authentication service"
    )
    auth_service_api_key: str = Field(
        ..., alias="AUTH_SERVICE_API_KEY", description="API key for auth service"
    )
    auth_service_timeout: int = Field(
        default=10,
        alias="AUTH_SERVICE_TIMEOUT",
        description="Auth service timeout in seconds",
    )

    # Workflow Service Configuration
    workflow_service_url: str = Field(
        ..., alias="WORKFLOW_SERVICE_URL", description="URL of the workflow service"
    )
    workflow_service_api_key: str = Field(
        ...,
        alias="WORKFLOW_SERVICE_API_KEY",
        description="API key for workflow service",
    )
    workflow_service_timeout: int = Field(
        default=60,
        alias="WORKFLOW_SERVICE_TIMEOUT",
        description="Workflow service timeout in seconds",
    )

    # Google Calendar Configuration
    google_calendar_webhook_url: str = Field(
        default=None,
        alias="GOOGLE_CALENDAR_WEBHOOK_URL",
        description="Public URL for Google Calendar webhooks",
    )
    google_calendar_webhook_secret: Optional[str] = Field(
        default=None,
        alias="GOOGLE_CALENDAR_WEBHOOK_SECRET",
        description="Secret key for Google Calendar webhook verification",
    )

    # Redis Configuration
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        alias="REDIS_URL",
        description="Redis connection URL",
    )

    # Celery Configuration
    celery_broker_url: Optional[str] = Field(
        None, alias="CELERY_BROKER_URL", description="Celery broker URL"
    )
    celery_result_backend: Optional[str] = Field(
        None, alias="CELERY_RESULT_BACKEND", description="Celery result backend URL"
    )

    # Security
    secret_key: str = Field(
        ..., alias="SECRET_KEY", description="Secret key for cryptographic operations"
    )
    api_key: str = Field(
        ..., alias="API_KEY", description="API key for internal service authentication"
    )

    # HTTP Configuration
    http_timeout: int = Field(
        default=30, alias="HTTP_TIMEOUT", description="Default HTTP timeout in seconds"
    )

    # Retry Configuration
    max_retry_attempts: int = Field(
        default=5,
        alias="MAX_RETRY_ATTEMPTS",
        description="Maximum number of retry attempts",
    )
    retry_backoff_factor: float = Field(
        default=2.0,
        alias="RETRY_BACKOFF_FACTOR",
        description="Exponential backoff factor",
    )
    retry_max_delay: int = Field(
        default=300,
        alias="RETRY_MAX_DELAY",
        description="Maximum retry delay in seconds",
    )

    # Rate Limiting
    rate_limit_requests_per_minute: int = Field(
        default=100,
        alias="RATE_LIMIT_REQUESTS_PER_MINUTE",
        description="Rate limit requests per minute",
    )
    rate_limit_burst: int = Field(
        default=20, alias="RATE_LIMIT_BURST", description="Rate limit burst capacity"
    )

    # Monitoring
    enable_metrics: bool = Field(
        default=True, alias="ENABLE_METRICS", description="Enable Prometheus metrics"
    )
    metrics_port: int = Field(
        default=9090, alias="METRICS_PORT", description="Port for metrics endpoint"
    )

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is one of the allowed values."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v.upper()

    @field_validator("log_format")
    @classmethod
    def validate_log_format(cls, v: str) -> str:
        """Validate log format is one of the allowed values."""
        allowed_formats = ["json", "text"]
        if v.lower() not in allowed_formats:
            raise ValueError(f"Log format must be one of: {allowed_formats}")
        return v.lower()

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v: str) -> str:
        """Validate database URL format."""
        if not v.startswith(("postgresql://", "postgresql+psycopg2://")):
            raise ValueError("Database URL must be a PostgreSQL connection string")
        return v

    @field_validator("redis_url")
    @classmethod
    def validate_redis_url(cls, v: str) -> str:
        """Validate Redis URL format."""
        if not v.startswith("redis://"):
            raise ValueError("Redis URL must start with 'redis://'")
        return v

    @field_validator("port", "metrics_port")
    @classmethod
    def validate_port(cls, v: int) -> int:
        """Validate port numbers are in valid range."""
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v

    @field_validator("max_retry_attempts")
    @classmethod
    def validate_max_retry_attempts(cls, v: int) -> int:
        """Validate max retry attempts is positive."""
        if v < 1:
            raise ValueError("Max retry attempts must be at least 1")
        return v

    @field_validator("retry_backoff_factor")
    @classmethod
    def validate_retry_backoff_factor(cls, v: float) -> float:
        """Validate retry backoff factor is positive."""
        if v <= 0:
            raise ValueError("Retry backoff factor must be positive")
        return v

    def get_celery_broker_url(self) -> str:
        """Get Celery broker URL, defaulting to Redis URL if not set."""
        return self.celery_broker_url or self.redis_url

    def get_celery_result_backend(self) -> str:
        """Get Celery result backend URL, defaulting to Redis URL if not set."""
        return self.celery_result_backend or self.redis_url

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "validate_assignment": True,
        "extra": "ignore",
    }


def get_settings() -> Settings:
    """
    Get application settings.

    Returns:
        Settings: Application settings instance
    """
    return Settings()


def get_database_url() -> str:
    """
    Get database URL for SQLAlchemy.

    Returns:
        str: Database connection URL
    """
    return get_settings().database_url


def is_development() -> bool:
    """
    Check if the application is running in development mode.

    Returns:
        bool: True if in development mode
    """
    return get_settings().debug


def is_production() -> bool:
    """
    Check if the application is running in production mode.

    Returns:
        bool: True if in production mode
    """
    return not get_settings().debug
