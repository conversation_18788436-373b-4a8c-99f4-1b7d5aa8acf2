"""
Pydantic schemas for trigger-related API operations.

This module contains all Pydantic models for trigger creation, updates,
and responses.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

# Simplified - remove complex validation imports


class TriggerCreate(BaseModel):
    """Schema for creating a new trigger."""

    user_id: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="ID of the user creating the trigger",
    )
    workflow_id: str = Field(
        ..., min_length=1, max_length=255, description="ID of the workflow to execute"
    )
    trigger_type: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Type of trigger (e.g., 'google_calendar')",
    )
    trigger_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Human-readable name for the trigger",
    )
    trigger_config: Dict[str, Any] = Field(
        ..., description="Adapter-specific configuration"
    )
    event_types: List[str] = Field(
        ..., min_items=1, description="List of event types to monitor"
    )

    # Simplified - no complex validation for now

    class Config:
        use_enum_values = True
        schema_extra = {
            "example": {
                "user_id": "user-123",
                "workflow_id": "workflow-456",
                "trigger_type": "google_calendar",
                "trigger_name": "Meeting Reminder Workflow",
                "trigger_config": {
                    "calendar_id": "primary",
                    "use_polling": False,
                    "poll_interval_seconds": 60,
                    "webhook_ttl": 604800,
                    "event_filters": {
                        "title_contains": ["meeting", "call"],
                        "attendee_count_min": 2,
                    },
                },
                "event_types": ["created", "updated"],
            }
        }


class TriggerUpdate(BaseModel):
    """Schema for updating an existing trigger."""

    trigger_name: Optional[str] = Field(
        None, description="Human-readable name for the trigger"
    )
    trigger_config: Optional[Dict[str, Any]] = Field(
        None, description="Adapter-specific configuration"
    )
    event_types: Optional[List[str]] = Field(
        None, description="List of event types to monitor"
    )
    is_active: Optional[bool] = Field(None, description="Whether the trigger is active")


class TriggerResponse(BaseModel):
    """Schema for trigger API responses."""

    id: UUID = Field(..., description="Unique identifier for the trigger")
    user_id: str = Field(..., description="ID of the user who owns the trigger")
    workflow_id: str = Field(..., description="ID of the associated workflow")
    trigger_type: str = Field(..., description="Type of trigger")
    trigger_name: str = Field(..., description="Human-readable name for the trigger")
    trigger_config: Dict[str, Any] = Field(
        ..., description="Adapter-specific configuration"
    )
    event_types: List[str] = Field(
        ..., description="List of event types being monitored"
    )
    is_active: bool = Field(..., description="Whether the trigger is active")
    created_at: datetime = Field(..., description="When the trigger was created")
    updated_at: datetime = Field(..., description="When the trigger was last updated")
    last_triggered_at: Optional[datetime] = Field(
        None, description="When the trigger was last activated"
    )

    class Config:
        from_attributes = True


class TriggerListResponse(BaseModel):
    """Schema for paginated trigger list responses."""

    triggers: List[TriggerResponse] = Field(..., description="List of triggers")
    total: int = Field(..., description="Total number of triggers")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")


class TriggerToggleRequest(BaseModel):
    """Schema for enabling/disabling triggers."""

    is_active: bool = Field(
        ..., description="Whether to activate or deactivate the trigger"
    )


class TriggerExecutionResponse(BaseModel):
    """Schema for trigger execution responses."""

    id: UUID = Field(..., description="Unique identifier for the execution")
    trigger_id: UUID = Field(..., description="ID of the trigger that was executed")
    event_data: Dict[str, Any] = Field(
        ..., description="Event data that triggered the execution"
    )
    workflow_execution_id: Optional[str] = Field(
        None, description="Correlation ID from workflow service"
    )
    status: str = Field(..., description="Execution status")
    error_message: Optional[str] = Field(
        None, description="Error message if execution failed"
    )
    retry_count: int = Field(..., description="Number of retry attempts")
    executed_at: datetime = Field(..., description="When the execution was initiated")
    completed_at: Optional[datetime] = Field(
        None, description="When the execution was completed"
    )

    class Config:
        from_attributes = True


class TriggerStatsResponse(BaseModel):
    """Schema for trigger statistics responses."""

    total_triggers: int = Field(..., description="Total number of triggers")
    active_triggers: int = Field(..., description="Number of active triggers")
    inactive_triggers: int = Field(..., description="Number of inactive triggers")
    triggers_by_type: Dict[str, int] = Field(
        ..., description="Count of triggers by type"
    )
    recent_executions: int = Field(
        ..., description="Number of executions in the last 24 hours"
    )
    success_rate: float = Field(..., description="Success rate percentage")


class AdapterHealthResponse(BaseModel):
    """Schema for adapter health responses."""

    adapter_name: str = Field(..., description="Name of the adapter")
    is_healthy: bool = Field(..., description="Whether the adapter is healthy")
    last_check: datetime = Field(..., description="When the health check was performed")
    error_message: Optional[str] = Field(None, description="Error message if unhealthy")
    active_triggers: int = Field(
        ..., description="Number of active triggers for this adapter"
    )
    external_service_status: Optional[Dict[str, Any]] = Field(
        None, description="External service status details"
    )


class TriggerFilterRequest(BaseModel):
    """Schema for filtering triggers."""

    user_id: Optional[str] = Field(None, description="Filter by user ID")
    workflow_id: Optional[str] = Field(None, description="Filter by workflow ID")
    trigger_type: Optional[str] = Field(None, description="Filter by trigger type")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    created_after: Optional[datetime] = Field(
        None, description="Filter by creation date"
    )
    created_before: Optional[datetime] = Field(
        None, description="Filter by creation date"
    )
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Number of items per page")


class GoogleCalendarTriggerConfig(BaseModel):
    """Schema for Google Calendar trigger configuration."""

    calendar_id: str = Field(
        default="primary",
        description="Google Calendar ID to monitor (default: 'primary')",
    )
    use_polling: bool = Field(
        default=False, description="Force polling mode instead of webhooks"
    )
    poll_interval_seconds: int = Field(
        default=60, ge=15, le=3600, description="Polling interval in seconds (15-3600)"
    )
    webhook_ttl: int = Field(
        default=604800,
        ge=3600,
        le=2592000,
        description="Webhook subscription TTL in seconds (1 hour - 30 days)",
    )
    event_filters: Optional[Dict[str, Any]] = Field(
        default=None, description="Optional event filtering criteria"
    )

    class Config:
        schema_extra = {
            "example": {
                "calendar_id": "primary",
                "use_polling": False,
                "poll_interval_seconds": 60,
                "webhook_ttl": 604800,
                "event_filters": {
                    "title_contains": ["meeting", "call"],
                    "attendee_count_min": 2,
                },
            }
        }


class WebhookChannelInfo(BaseModel):
    """Schema for webhook channel information."""

    channel_id: str = Field(..., description="Unique channel identifier")
    resource_id: str = Field(..., description="Google Calendar resource ID")
    trigger_id: str = Field(..., description="Associated trigger ID")
    user_id: str = Field(..., description="User ID who owns this channel")
    calendar_id: str = Field(..., description="Calendar ID being monitored")
    expires_at: datetime = Field(..., description="When the channel expires")
    is_expired: bool = Field(..., description="Whether the channel has expired")
    created_at: datetime = Field(..., description="When the channel was created")


class WebhookStatusResponse(BaseModel):
    """Schema for webhook status response."""

    active_channels: List[WebhookChannelInfo] = Field(
        ..., description="List of active webhook channels"
    )
    total_channels: int = Field(..., description="Total number of active channels")
    expired_channels: int = Field(..., description="Number of expired channels")


class WebhookCleanupResponse(BaseModel):
    """Schema for webhook cleanup response."""

    cleaned_channels: int = Field(..., description="Number of channels cleaned up")
    remaining_channels: int = Field(..., description="Number of channels remaining")
    errors: List[str] = Field(default=[], description="Any errors during cleanup")


class WebhookStopRequest(BaseModel):
    """Schema for stopping a webhook channel."""

    channel_id: str = Field(..., description="Channel ID to stop")


class WebhookStopResponse(BaseModel):
    """Schema for webhook stop response."""

    success: bool = Field(
        ..., description="Whether the channel was stopped successfully"
    )
    message: str = Field(..., description="Result message")
    channel_id: str = Field(..., description="Channel ID that was stopped")
