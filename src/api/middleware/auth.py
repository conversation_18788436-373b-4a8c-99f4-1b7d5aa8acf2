"""
Authentication middleware for the Trigger Service API.

This module provides authentication middleware that validates API keys
and manages user authentication for protected endpoints.
"""

from typing import Optional
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)
security = HTTPBearer(auto_error=False)


class AuthMiddleware(BaseHTTPMiddleware):
    """
    Authentication middleware for API key validation.

    This middleware validates API keys for protected endpoints and
    sets user context for authenticated requests.
    """

    def __init__(self, app, protected_paths: Optional[list] = None):
        """
        Initialize the authentication middleware.

        Args:
            app: FastAPI application instance
            protected_paths: List of path patterns that require authentication
        """
        super().__init__(app)
        self.protected_paths = protected_paths or [
            "/api/v1/triggers",
            "/api/v1/metrics",
        ]
        self.settings = get_settings()

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and validate authentication if required.

        Args:
            request: Incoming HTTP request
            call_next: Next middleware or endpoint handler

        Returns:
            Response: HTTP response
        """
        # Skip authentication for public endpoints
        if not self._requires_auth(request.url.path):
            return await call_next(request)

        # Extract and validate API key
        try:
            api_key = self._extract_api_key(request)
            if not api_key:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Missing API key",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Validate API key
            user_id = await self._validate_api_key(api_key)
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid API key",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Set user context in request state
            request.state.user_id = user_id
            request.state.authenticated = True

            logger.info(
                f"Authenticated request for user {user_id}",
                path=request.url.path,
                method=request.method,
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service error",
            )

        return await call_next(request)

    def _requires_auth(self, path: str) -> bool:
        """
        Check if the given path requires authentication.

        Args:
            path: Request path

        Returns:
            bool: True if authentication is required
        """
        # Skip authentication for health check and docs
        public_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/health",
            "/api/v1/health/detailed",  # Make detailed health public for monitoring
        ]

        # Check exact matches first
        if path in public_paths:
            return False

        # Check if path starts with webhook patterns (webhooks should be public)
        webhook_patterns = ["/api/v1/webhooks"]
        for webhook_pattern in webhook_patterns:
            if path.startswith(webhook_pattern):
                return False

        # Check if path matches any protected pattern
        for protected_path in self.protected_paths:
            if path.startswith(protected_path):
                return True

        return False

    def _extract_api_key(self, request: Request) -> Optional[str]:
        """
        Extract API key from request headers.

        Args:
            request: HTTP request

        Returns:
            Optional[str]: API key if found
        """
        # Try Authorization header first
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove "Bearer " prefix

        # Try X-API-Key header
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return api_key

        # Try query parameter (less secure, for development only)
        if self.settings.debug:
            return request.query_params.get("api_key")

        return None

    async def _validate_api_key(self, api_key: str) -> Optional[str]:
        """
        Validate API key and return associated user ID.

        Args:
            api_key: API key to validate

        Returns:
            Optional[str]: User ID if valid, None otherwise
        """
        # For development, accept the configured API key
        if api_key == self.settings.api_key:
            return "dev-user"  # Default development user

        # In production, this would validate against a database or auth service
        # For now, implement basic validation
        if len(api_key) >= 32:  # Basic length check
            # This is a placeholder - implement actual validation logic
            # Could integrate with your auth service here
            return f"user-{hash(api_key) % 10000}"

        return None


def get_current_user(request: Request) -> str:
    """
    Get the current authenticated user from request state.

    Args:
        request: HTTP request with user context

    Returns:
        str: User ID

    Raises:
        HTTPException: If user is not authenticated
    """
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required"
        )

    return request.state.user_id


def require_auth(request: Request) -> bool:
    """
    Check if request is authenticated.

    Args:
        request: HTTP request

    Returns:
        bool: True if authenticated

    Raises:
        HTTPException: If not authenticated
    """
    if not getattr(request.state, "authenticated", False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required"
        )

    return True
