"""
Request logging middleware for the Trigger Service.

This module provides middleware to log all incoming requests and responses
with detailed information for monitoring and debugging.
"""

import time
from typing import Dict, Any
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from src.utils.logger import get_logger
from src.api.middleware.correlation import get_correlation_id

logger = get_logger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to log all HTTP requests and responses.

    This middleware logs request details, response status, and timing
    information for monitoring and debugging purposes.
    """

    def __init__(
        self, app, log_request_body: bool = False, log_response_body: bool = False
    ):
        """
        Initialize the logging middleware.

        Args:
            app: FastAPI application instance
            log_request_body: Whether to log request bodies (security consideration)
            log_response_body: Whether to log response bodies (performance consideration)
        """
        super().__init__(app)
        self.log_request_body = log_request_body
        self.log_response_body = log_response_body

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and log details.

        Args:
            request: Incoming HTTP request
            call_next: Next middleware or endpoint handler

        Returns:
            Response: HTTP response
        """
        start_time = time.time()

        # Log request
        await self._log_request(request)

        # Process request
        response = await call_next(request)

        # Calculate processing time
        process_time = time.time() - start_time

        # Log response
        self._log_response(request, response, process_time)

        # Add timing header
        response.headers["X-Process-Time"] = str(process_time)

        return response

    async def _log_request(self, request: Request) -> None:
        """
        Log incoming request details.

        Args:
            request: HTTP request to log
        """
        correlation_id = get_correlation_id(request)

        # Basic request info
        log_data = {
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "user_agent": request.headers.get("user-agent"),
            "client_ip": self._get_client_ip(request),
            "correlation_id": correlation_id,
        }

        # Add request body if enabled (be careful with sensitive data)
        if self.log_request_body and request.method in ["POST", "PUT", "PATCH"]:
            try:
                # Note: This consumes the request body, so we need to be careful
                # In production, consider logging only for specific endpoints
                body = await request.body()
                if body:
                    log_data["request_body_size"] = len(body)
                    # Only log body for non-sensitive endpoints
                    if not self._is_sensitive_endpoint(request.url.path):
                        log_data["request_body"] = body.decode(
                            "utf-8", errors="ignore"
                        )[
                            :1000
                        ]  # Limit size
            except Exception as e:
                log_data["request_body_error"] = str(e)

        logger.info("HTTP request started", **log_data)

    def _log_response(
        self, request: Request, response: Response, process_time: float
    ) -> None:
        """
        Log response details.

        Args:
            request: HTTP request
            response: HTTP response
            process_time: Time taken to process the request
        """
        correlation_id = get_correlation_id(request)

        log_data = {
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "process_time": round(process_time, 4),
            "correlation_id": correlation_id,
        }

        # Add response body size if available
        if hasattr(response, "body"):
            log_data["response_body_size"] = len(response.body) if response.body else 0

        # Log level based on status code
        if response.status_code >= 500:
            logger.error("HTTP request failed", **log_data)
        elif response.status_code >= 400:
            logger.warning("HTTP request error", **log_data)
        else:
            logger.info("HTTP request completed", **log_data)

    def _get_client_ip(self, request: Request) -> str:
        """
        Get the client IP address from request.

        Args:
            request: HTTP request

        Returns:
            str: Client IP address
        """
        # Check for forwarded headers (when behind proxy/load balancer)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"

    def _is_sensitive_endpoint(self, path: str) -> bool:
        """
        Check if the endpoint handles sensitive data.

        Args:
            path: Request path

        Returns:
            bool: True if endpoint is sensitive
        """
        sensitive_patterns = ["/auth", "/login", "/password", "/token", "/credentials"]

        return any(pattern in path.lower() for pattern in sensitive_patterns)


class PerformanceLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware specifically for performance monitoring.

    This middleware logs detailed performance metrics for slow requests
    and provides insights into application performance.
    """

    def __init__(self, app, slow_request_threshold: float = 1.0):
        """
        Initialize the performance logging middleware.

        Args:
            app: FastAPI application instance
            slow_request_threshold: Threshold in seconds for slow request logging
        """
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and log performance metrics.

        Args:
            request: Incoming HTTP request
            call_next: Next middleware or endpoint handler

        Returns:
            Response: HTTP response
        """
        start_time = time.time()

        response = await call_next(request)

        process_time = time.time() - start_time

        # Log slow requests
        if process_time > self.slow_request_threshold:
            correlation_id = get_correlation_id(request)

            logger.warning(
                "Slow request detected",
                method=request.method,
                path=request.url.path,
                process_time=round(process_time, 4),
                threshold=self.slow_request_threshold,
                status_code=response.status_code,
                correlation_id=correlation_id,
            )

        return response
