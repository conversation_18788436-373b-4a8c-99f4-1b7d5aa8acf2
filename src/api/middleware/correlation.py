"""
Correlation ID middleware for request tracing.

This module provides middleware to generate and manage correlation IDs
for request tracing across the application.
"""

import uuid
from typing import Op<PERSON>
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from src.utils.logger import get_logger

logger = get_logger(__name__)


class CorrelationMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add correlation IDs to requests for tracing.
    
    This middleware generates or extracts correlation IDs from requests
    and adds them to the response headers and request state.
    """
    
    def __init__(self, app, header_name: str = "X-Correlation-ID"):
        """
        Initialize the correlation middleware.
        
        Args:
            app: FastAPI application instance
            header_name: Name of the correlation ID header
        """
        super().__init__(app)
        self.header_name = header_name
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and add correlation ID.
        
        Args:
            request: Incoming HTTP request
            call_next: Next middleware or endpoint handler
            
        Returns:
            Response: HTTP response with correlation ID header
        """
        # Extract or generate correlation ID
        correlation_id = self._get_or_generate_correlation_id(request)
        
        # Add to request state for use in handlers
        request.state.correlation_id = correlation_id
        
        # Process the request
        response = await call_next(request)
        
        # Add correlation ID to response headers
        response.headers[self.header_name] = correlation_id
        
        return response
    
    def _get_or_generate_correlation_id(self, request: Request) -> str:
        """
        Get correlation ID from request headers or generate a new one.
        
        Args:
            request: HTTP request
            
        Returns:
            str: Correlation ID
        """
        # Try to get from request headers
        correlation_id = request.headers.get(self.header_name)
        
        if correlation_id:
            logger.debug(f"Using existing correlation ID: {correlation_id}")
            return correlation_id
        
        # Generate new correlation ID
        correlation_id = str(uuid.uuid4())
        logger.debug(f"Generated new correlation ID: {correlation_id}")
        
        return correlation_id


def get_correlation_id(request: Request) -> Optional[str]:
    """
    Get the correlation ID from request state.
    
    Args:
        request: HTTP request
        
    Returns:
        Optional[str]: Correlation ID if available
    """
    return getattr(request.state, "correlation_id", None)
