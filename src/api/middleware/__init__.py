"""
API middleware package for the Trigger Service.

This package contains all middleware components for request processing
including authentication, logging, error handling, and correlation tracking.
"""

from .auth import AuthMiddleware, get_current_user, require_auth
from .error_handler import <PERSON>rrorHandlerMiddleware
from .correlation import CorrelationMiddleware, get_correlation_id
from .logging import LoggingMiddleware, PerformanceLoggingMiddleware

__all__ = [
    "AuthMiddleware",
    "ErrorHandlerMiddleware",
    "CorrelationMiddleware",
    "LoggingMiddleware",
    "PerformanceLoggingMiddleware",
    "get_current_user",
    "require_auth",
    "get_correlation_id",
]
