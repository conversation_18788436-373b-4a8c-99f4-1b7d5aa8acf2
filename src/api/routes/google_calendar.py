"""
Google Calendar adapter management API routes.

This module provides endpoints for managing Google Calendar webhook channels,
monitoring status, and performing maintenance operations.
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse

from src.schemas.trigger import (
    WebhookStatusResponse,
    WebhookCleanupResponse,
    WebhookStopRequest,
    WebhookStopResponse,
)
from src.adapters.google_calendar import GoogleCalendarAdapter
from src.api.middleware.auth import get_current_user, require_auth
from src.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/adapters/google_calendar", tags=["Google Calendar"])


def get_google_calendar_adapter() -> GoogleCalendarAdapter:
    """Get Google Calendar adapter instance."""
    # In a real application, this would be injected via dependency injection
    if not hasattr(get_google_calendar_adapter, "_instance"):
        get_google_calendar_adapter._instance = GoogleCalendarAdapter()
    return get_google_calendar_adapter._instance


@router.get(
    "/webhook_status",
    response_model=WebhookStatusResponse,
    summary="Get webhook channel status",
    description="Get the status of all active Google Calendar webhook channels",
    responses={
        200: {"description": "Webhook status retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def get_webhook_status(
    request: Request,
    adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
) -> WebhookStatusResponse:
    """
    Get the status of all active Google Calendar webhook channels.

    Returns information about active channels, expiration times, and user associations.
    """
    try:
        # Require authentication
        require_auth(request)
        authenticated_user = get_current_user(request)
        
        logger.info(f"Getting webhook status for user {authenticated_user}")
        
        # Get webhook status from adapter
        status_data = await adapter.get_webhook_status()
        
        if "error" in status_data:
            raise HTTPException(status_code=500, detail=status_data["error"])
        
        return WebhookStatusResponse(**status_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get webhook status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get webhook status")


@router.post(
    "/cleanup_channels",
    response_model=WebhookCleanupResponse,
    summary="Clean up expired webhook channels",
    description="Remove expired Google Calendar webhook channels and clean up resources",
    responses={
        200: {"description": "Cleanup completed successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def cleanup_expired_channels(
    request: Request,
    adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
) -> WebhookCleanupResponse:
    """
    Clean up expired Google Calendar webhook channels.

    This endpoint removes expired webhook subscriptions and cleans up associated resources.
    """
    try:
        # Require authentication
        require_auth(request)
        authenticated_user = get_current_user(request)
        
        logger.info(f"Cleaning up expired channels for user {authenticated_user}")
        
        # Get initial status
        initial_status = await adapter.get_webhook_status()
        initial_count = initial_status.get("total_channels", 0)
        
        # Perform cleanup
        cleaned_count = await adapter.cleanup_expired_channels()
        
        # Get final status
        final_status = await adapter.get_webhook_status()
        remaining_count = final_status.get("total_channels", 0)
        
        logger.info(f"Cleaned up {cleaned_count} expired channels")
        
        return WebhookCleanupResponse(
            cleaned_channels=cleaned_count,
            remaining_channels=remaining_count,
            errors=[],
        )
        
    except Exception as e:
        logger.error(f"Failed to cleanup expired channels", error=str(e))
        return WebhookCleanupResponse(
            cleaned_channels=0,
            remaining_channels=0,
            errors=[str(e)],
        )


@router.post(
    "/stop_channel",
    response_model=WebhookStopResponse,
    summary="Stop a webhook channel",
    description="Stop a specific Google Calendar webhook channel",
    responses={
        200: {"description": "Channel stopped successfully"},
        401: {"description": "Authentication required"},
        404: {"description": "Channel not found"},
        500: {"description": "Internal server error"},
    },
)
async def stop_webhook_channel(
    request_data: WebhookStopRequest,
    request: Request,
    adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
) -> WebhookStopResponse:
    """
    Stop a specific Google Calendar webhook channel.

    This endpoint stops a webhook channel and removes it from active monitoring.
    """
    try:
        # Require authentication
        require_auth(request)
        authenticated_user = get_current_user(request)
        
        channel_id = request_data.channel_id
        logger.info(f"Stopping webhook channel {channel_id} for user {authenticated_user}")
        
        # Stop the channel
        success = await adapter.stop_webhook_channel(channel_id)
        
        if success:
            return WebhookStopResponse(
                success=True,
                message=f"Channel {channel_id} stopped successfully",
                channel_id=channel_id,
            )
        else:
            return WebhookStopResponse(
                success=False,
                message=f"Channel {channel_id} not found or already stopped",
                channel_id=channel_id,
            )
        
    except Exception as e:
        logger.error(f"Failed to stop webhook channel {request_data.channel_id}", error=str(e))
        return WebhookStopResponse(
            success=False,
            message=f"Error stopping channel: {str(e)}",
            channel_id=request_data.channel_id,
        )


@router.get(
    "/health",
    summary="Get Google Calendar adapter health",
    description="Get health status and metrics for the Google Calendar adapter",
    responses={
        200: {"description": "Health status retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def get_adapter_health(
    request: Request,
    adapter: GoogleCalendarAdapter = Depends(get_google_calendar_adapter),
) -> Dict[str, Any]:
    """
    Get health status and metrics for the Google Calendar adapter.

    Returns information about adapter health, active triggers, and webhook status.
    """
    try:
        # Require authentication
        require_auth(request)
        authenticated_user = get_current_user(request)
        
        logger.debug(f"Getting adapter health for user {authenticated_user}")
        
        # Get webhook status
        webhook_status = await adapter.get_webhook_status()
        
        # Get basic health info
        health_info = {
            "adapter_name": "google_calendar",
            "is_healthy": True,
            "webhook_channels": {
                "total": webhook_status.get("total_channels", 0),
                "expired": webhook_status.get("expired_channels", 0),
                "active": webhook_status.get("total_channels", 0) - webhook_status.get("expired_channels", 0),
            },
            "polling_tasks": len(getattr(adapter, "_polling_tasks", {})),
            "cached_credentials": len(getattr(adapter, "_user_credentials", {})),
        }
        
        return health_info
        
    except Exception as e:
        logger.error(f"Failed to get adapter health", error=str(e))
        return {
            "adapter_name": "google_calendar",
            "is_healthy": False,
            "error": str(e),
        }
