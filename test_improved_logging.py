#!/usr/bin/env python3
"""
Test script to demonstrate the improved logging system.
Shows how the new readable logging format works.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent / "src"))

from utils.logger import setup_logging, get_logger
from adapters.google_calendar import GoogleCalendarAdapter
from adapters.base import TriggerConfiguration
from uuid import uuid4


async def test_improved_logging():
    """Test the improved logging system with Google Calendar adapter."""
    print("🧪 Testing Improved Logging System")
    print("=" * 50)
    
    # Setup logging in text format for readability
    setup_logging(log_level="DEBUG", log_format="text")
    
    # Get a logger to demonstrate the new format
    logger = get_logger("test_logging")
    
    # Test different log levels and contexts
    logger.info("🚀 Starting logging test", test_phase="initialization")
    
    logger.debug("📋 Debug information", 
                 details="This is a debug message with context",
                 component="logging_test")
    
    logger.warning("⚠️ Warning message", 
                   issue="This is a warning with context",
                   recommendation="Check configuration")
    
    logger.error("❌ Error message", 
                 error_type="TestError",
                 error_message="This is a test error",
                 component="logging_test")
    
    # Test with user and trigger context
    logger.info("👤 User operation", 
                user_id="test_user_123",
                operation="login",
                success=True)
    
    logger.info("🎯 Trigger operation",
                trigger_id="trigger_456", 
                trigger_type="google_calendar",
                trigger_name="Test Calendar Trigger",
                user_id="test_user_123",
                calendar_id="primary")
    
    # Test Google Calendar adapter logging
    print("\n📅 Testing Google Calendar Adapter Logging:")
    print("-" * 50)
    
    adapter = GoogleCalendarAdapter()
    
    # Create a test trigger configuration
    trigger_config = TriggerConfiguration(
        trigger_id=uuid4(),
        user_id="demo_user",
        workflow_id="demo_workflow",
        trigger_type="google_calendar",
        trigger_name="Demo Calendar Trigger",
        config={
            "calendar_id": "primary",
            "use_polling": True,
            "poll_interval_seconds": 30,
        },
        event_types=["created", "updated"],
        is_active=True,
    )
    
    # Test adapter logging (this will show credential loading attempts)
    logger.info("🔧 Testing adapter setup", 
                trigger_id=trigger_config.trigger_id,
                user_id=trigger_config.user_id)
    
    # This will demonstrate the improved logging in action
    # Note: This will fail without proper credentials, but will show the logging
    try:
        success = await adapter.setup_trigger(trigger_config)
        if success:
            logger.info("✅ Adapter setup successful")
            # Clean up
            await adapter.remove_trigger(trigger_config.trigger_id)
        else:
            logger.warning("⚠️ Adapter setup failed (expected without credentials)")
    except Exception as e:
        logger.error("❌ Adapter setup error", 
                     error_type=type(e).__name__,
                     error_message=str(e))
    
    print("\n✅ Logging test completed!")
    print("\n📝 Logging Improvements:")
    print("   ✅ Colorized output with emojis for easy scanning")
    print("   ✅ Structured context grouped by category")
    print("   ✅ User, trigger, and error information clearly separated")
    print("   ✅ Timestamps in readable format")
    print("   ✅ Reduced noise from third-party libraries")
    print("   ✅ Clear visual hierarchy with indentation")


def test_json_logging():
    """Test JSON logging format for comparison."""
    print("\n🔄 Testing JSON Logging Format (for production):")
    print("-" * 50)
    
    # Setup JSON logging
    setup_logging(log_level="INFO", log_format="json")
    
    logger = get_logger("json_test")
    
    logger.info("Production log example",
                user_id="prod_user_123",
                trigger_id="prod_trigger_456",
                operation="webhook_received",
                calendar_id="primary",
                event_count=3)
    
    print("\n📝 JSON format is compact and machine-readable for production")


async def main():
    """Main test function."""
    print("📊 Logging System Improvement Test")
    print("Demonstrating the new readable logging format")
    print()
    
    try:
        # Test the improved text logging
        await test_improved_logging()
        
        # Test JSON logging for comparison
        test_json_logging()
        
        print("\n🎉 All logging tests completed!")
        print("\n📋 Configuration:")
        print("   - Set LOG_FORMAT=text in .env for readable development logs")
        print("   - Set LOG_FORMAT=json in .env for production logs")
        print("   - Set LOG_LEVEL=DEBUG for detailed information")
        print("   - Set LOG_LEVEL=INFO for normal operation")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
