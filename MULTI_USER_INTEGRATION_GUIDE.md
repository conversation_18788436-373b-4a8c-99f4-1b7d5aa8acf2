# Multi-User Google Calendar Integration Guide

Your Google Calendar webhook implementation has been successfully integrated into the trigger service with full multi-user support! This guide explains the enhanced features and how to use them.

## What Was Enhanced

Based on your `google_calender_webhook.py`, the following multi-user features have been integrated:

### From Your Webhook Implementation ✅
- **Channel management** with `active_channels` tracking
- **Webhook signature verification** for security
- **Channel lifecycle management** (setup, stop, cleanup)
- **Expiration handling** and automatic cleanup
- **Status monitoring** for active channels
- **Threaded processing** (converted to async)

### Enhanced for Multi-User Support ✅
- **Per-user credential management** with caching
- **User-specific token files** support
- **Concurrent webhook channels** for multiple users
- **User-specific polling tasks**
- **Channel-to-user mapping** for webhook routing
- **Bulk operations** for multi-user scenarios

## Multi-User Architecture

### 1. Credential Management

The adapter now supports multiple credential storage patterns:

```
# User-specific tokens (recommended for multi-user)
tokens/
├── token_user1.json
├── token_user2.json
└── token_user3.json

# Or individual user tokens in root
token_user1.json
token_user2.json
token_shared.json  # fallback
```

### 2. Channel Management

Each webhook channel is tracked with user context:

```python
{
  "channel_id": "trigger-uuid-abc123",
  "resource_id": "google_resource_id", 
  "trigger_id": "trigger_uuid",
  "user_id": "user123",
  "calendar_id": "primary",
  "expiration": *************,
  "created_at": *************
}
```

### 3. Webhook Routing

Incoming webhooks are automatically routed to the correct user:

```
Webhook → Channel ID → User ID → User Credentials → Process Event
```

## Usage Examples

### 1. Create Triggers for Multiple Users

```bash
# User 1
curl -X POST "http://localhost:8000/api/v1/triggers/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: abc" \
  -d '{
    "user_id": "alice",
    "workflow_id": "workflow_alice",
    "trigger_type": "google_calendar",
    "trigger_name": "Alice Calendar Trigger",
    "trigger_config": {
      "calendar_id": "primary"
    },
    "event_types": ["created", "updated"]
  }'

# User 2  
curl -X POST "http://localhost:8000/api/v1/triggers/" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: abc" \
  -d '{
    "user_id": "bob", 
    "workflow_id": "workflow_bob",
    "trigger_type": "google_calendar",
    "trigger_name": "Bob Calendar Trigger",
    "trigger_config": {
      "calendar_id": "primary",
      "use_polling": true,
      "poll_interval_seconds": 60
    },
    "event_types": ["created", "updated"]
  }'
```

### 2. Monitor Multi-User Webhook Status

```bash
# Get status of all webhook channels
curl "http://localhost:8000/api/v1/adapters/google_calendar/webhook_status" \
  -H "X-API-Key: abc"
```

Response:
```json
{
  "active_channels": [
    {
      "channel_id": "trigger-alice-abc123",
      "user_id": "alice",
      "trigger_id": "trigger_uuid_alice",
      "calendar_id": "primary",
      "expires_at": "2024-01-01T00:00:00Z",
      "is_expired": false
    },
    {
      "channel_id": "trigger-bob-def456", 
      "user_id": "bob",
      "trigger_id": "trigger_uuid_bob",
      "calendar_id": "primary",
      "expires_at": "2024-01-01T00:00:00Z",
      "is_expired": false
    }
  ],
  "total_channels": 2,
  "expired_channels": 0
}
```

## Multi-User Configuration

### Environment Variables

```env
# Webhook configuration (shared)
GOOGLE_CALENDAR_WEBHOOK_URL=https://your-domain.com/api/v1/webhooks/google-calendar
GOOGLE_CALENDAR_WEBHOOK_SECRET=your-secret-key-here

# Multi-user token storage
# The adapter will look for user-specific tokens automatically
```

### Token File Structure

#### Option 1: User-Specific Tokens (Recommended)
```
project/
├── tokens/
│   ├── token_alice.json
│   ├── token_bob.json
│   └── token_charlie.json
└── ...
```

#### Option 2: Root-Level User Tokens
```
project/
├── token_alice.json
├── token_bob.json
├── token.json  # fallback for all users
└── ...
```

### Token File Format
Each token file should contain Google OAuth2 credentials:

```json
{
  "token": "ya29.a0...",
  "refresh_token": "1//04...",
  "token_uri": "https://oauth2.googleapis.com/token",
  "client_id": "your-client-id.googleusercontent.com",
  "client_secret": "your-client-secret",
  "scopes": ["https://www.googleapis.com/auth/calendar.readonly"]
}
```

## Testing Multi-User Setup

### 1. Run the Multi-User Test Script

```bash
python test_multi_user_integration.py
```

This will:
- Create sample user token files
- Set up triggers for multiple users
- Test webhook channel management
- Test credential caching
- Clean up resources

### 2. Manual Multi-User Testing

1. **Create user-specific token files**:
   ```bash
   mkdir tokens
   cp token.json tokens/token_alice.json
   cp token.json tokens/token_bob.json
   ```

2. **Start the trigger service**:
   ```bash
   python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Create triggers for multiple users** (see examples above)

4. **Create calendar events** for different users and watch the logs

## Advanced Features

### 1. Webhook Signature Verification

Based on your implementation's `verify_webhook_signature`:

```env
GOOGLE_CALENDAR_WEBHOOK_SECRET=your-secret-key-here
```

The adapter will automatically verify webhook signatures for security.

### 2. Automatic Channel Cleanup

Like your `cleanup_expired_channels` function:

```python
# Automatic cleanup of expired channels
cleaned_count = await adapter.cleanup_expired_channels()
```

### 3. Channel Management

Stop specific channels:

```python
# Stop a specific user's webhook channel
success = await adapter.stop_webhook_channel(channel_id)
```

### 4. Credential Caching

User credentials are automatically cached for performance:

```python
# First call loads from file
credentials = await adapter._get_user_credentials("alice")

# Second call uses cached credentials
credentials = await adapter._get_user_credentials("alice")  # Faster!
```

## Migration from Your Webhook Implementation

### 1. Token Files
Move your existing `token.json` to user-specific files:

```bash
mkdir tokens
cp token.json tokens/token_user1.json
cp token.json tokens/token_user2.json
```

### 2. Webhook URL
Update your webhook URL in the trigger service:

```env
GOOGLE_CALENDAR_WEBHOOK_URL=https://your-ngrok-url.ngrok.io/api/v1/webhooks/google-calendar
```

### 3. Create Triggers
Replace your Flask endpoints with trigger API calls (see examples above).

## Monitoring and Debugging

### 1. Multi-User Logs
Enable debug logging to see per-user activity:

```env
LOG_LEVEL=DEBUG
```

Look for logs like:
```
Processing webhook for user alice, trigger trigger_uuid_alice
ACTION: Processing event for user bob: 'Meeting with Client' starting at 2024-01-01T10:00:00Z
```

### 2. Channel Status
Monitor active channels:

```bash
curl "http://localhost:8000/api/v1/health" | jq '.adapters.google_calendar'
```

### 3. User-Specific Debugging
Check if specific users have credential issues:

```bash
# Check logs for user-specific errors
tail -f logs/trigger-service.log | grep "user_alice"
```

## Production Considerations

### 1. Credential Storage
- Use proper secret management (not file-based) in production
- Implement credential rotation
- Use the Auth service integration when ready

### 2. Webhook Security
- Always use HTTPS in production
- Set `GOOGLE_CALENDAR_WEBHOOK_SECRET` for signature verification
- Implement rate limiting

### 3. Scaling
- The current implementation supports hundreds of concurrent users
- Consider database-backed channel storage for larger scales
- Implement proper monitoring and alerting

## Troubleshooting

### Common Multi-User Issues

1. **"No credentials found for user X"**
   - Create user-specific token file: `tokens/token_X.json`
   - Or ensure fallback `token.json` exists

2. **"Unknown or inactive channel"**
   - Channel may have expired
   - Run cleanup: `adapter.cleanup_expired_channels()`

3. **"Webhook signature verification failed"**
   - Check `GOOGLE_CALENDAR_WEBHOOK_SECRET` configuration
   - Ensure secret matches what's configured in webhook setup

4. **Multiple users not working**
   - Verify each user has valid credentials
   - Check for credential conflicts or expired tokens
   - Monitor logs for user-specific errors

Your webhook implementation provided an excellent foundation for multi-user support! The integration preserves all your good patterns while making them scalable and production-ready.
