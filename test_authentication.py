#!/usr/bin/env python3
"""
Test script to verify authentication is working correctly.
Tests both Bearer token and X-API-Key authentication methods.
"""

import asyncio
import json
import httpx
from typing import Dict, Any


BASE_URL = "http://localhost:8000"
TEST_API_KEY = "abc"  # Default test API key


async def test_authentication_methods():
    """Test both authentication methods on various endpoints."""
    print("🔐 Testing Authentication Methods")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        # Test endpoints that should require authentication
        test_endpoints = [
            ("GET", "/api/v1/triggers/"),
            ("GET", "/api/v1/adapters/google_calendar/webhook_status"),
            ("GET", "/api/v1/adapters/google_calendar/health"),
            ("POST", "/api/v1/adapters/google_calendar/cleanup_channels"),
        ]
        
        for method, endpoint in test_endpoints:
            print(f"\n📍 Testing {method} {endpoint}")
            
            # Test 1: No authentication (should fail)
            print("   1. No authentication:")
            try:
                response = await client.request(method, f"{BASE_URL}{endpoint}")
                print(f"      Status: {response.status_code}")
                if response.status_code == 401:
                    print("      ✅ Correctly rejected (401 Unauthorized)")
                else:
                    print(f"      ❌ Expected 401, got {response.status_code}")
            except Exception as e:
                print(f"      ❌ Error: {e}")
            
            # Test 2: Bearer token authentication
            print("   2. Bearer token authentication:")
            try:
                headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
                response = await client.request(method, f"{BASE_URL}{endpoint}", headers=headers)
                print(f"      Status: {response.status_code}")
                if response.status_code in [200, 404]:  # 404 is OK for some endpoints
                    print("      ✅ Authentication successful")
                elif response.status_code == 401:
                    print("      ❌ Authentication failed (401)")
                else:
                    print(f"      ⚠️  Unexpected status: {response.status_code}")
            except Exception as e:
                print(f"      ❌ Error: {e}")
            
            # Test 3: X-API-Key authentication
            print("   3. X-API-Key authentication:")
            try:
                headers = {"X-API-Key": TEST_API_KEY}
                response = await client.request(method, f"{BASE_URL}{endpoint}", headers=headers)
                print(f"      Status: {response.status_code}")
                if response.status_code in [200, 404]:  # 404 is OK for some endpoints
                    print("      ✅ Authentication successful")
                elif response.status_code == 401:
                    print("      ❌ Authentication failed (401)")
                else:
                    print(f"      ⚠️  Unexpected status: {response.status_code}")
            except Exception as e:
                print(f"      ❌ Error: {e}")


async def test_trigger_creation():
    """Test creating a Google Calendar trigger with authentication."""
    print("\n🚀 Testing Google Calendar Trigger Creation")
    print("=" * 50)
    
    trigger_data = {
        "user_id": "test_user_auth",
        "workflow_id": "test_workflow_auth",
        "trigger_type": "google_calendar",
        "trigger_name": "Auth Test Trigger",
        "trigger_config": {
            "calendar_id": "primary",
            "use_polling": True,
            "poll_interval_seconds": 60,
            "webhook_ttl": 3600
        },
        "event_types": ["created", "updated"]
    }
    
    async with httpx.AsyncClient() as client:
        # Test with Bearer token
        print("📝 Testing trigger creation with Bearer token:")
        try:
            headers = {
                "Authorization": f"Bearer {TEST_API_KEY}",
                "Content-Type": "application/json"
            }
            response = await client.post(
                f"{BASE_URL}/api/v1/triggers/",
                headers=headers,
                json=trigger_data
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 201:
                print("   ✅ Trigger created successfully")
                result = response.json()
                trigger_id = result.get("id")
                print(f"   Trigger ID: {trigger_id}")
                
                # Clean up - delete the trigger
                if trigger_id:
                    delete_response = await client.delete(
                        f"{BASE_URL}/api/v1/triggers/{trigger_id}",
                        headers=headers
                    )
                    print(f"   Cleanup status: {delete_response.status_code}")
                    
            elif response.status_code == 401:
                print("   ❌ Authentication failed")
            else:
                print(f"   ⚠️  Unexpected status: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test with X-API-Key
        print("\n📝 Testing trigger creation with X-API-Key:")
        try:
            headers = {
                "X-API-Key": TEST_API_KEY,
                "Content-Type": "application/json"
            }
            response = await client.post(
                f"{BASE_URL}/api/v1/triggers/",
                headers=headers,
                json=trigger_data
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 201:
                print("   ✅ Trigger created successfully")
                result = response.json()
                trigger_id = result.get("id")
                print(f"   Trigger ID: {trigger_id}")
                
                # Clean up - delete the trigger
                if trigger_id:
                    delete_response = await client.delete(
                        f"{BASE_URL}/api/v1/triggers/{trigger_id}",
                        headers=headers
                    )
                    print(f"   Cleanup status: {delete_response.status_code}")
                    
            elif response.status_code == 401:
                print("   ❌ Authentication failed")
            else:
                print(f"   ⚠️  Unexpected status: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")


async def test_webhook_endpoints():
    """Test the new Google Calendar webhook management endpoints."""
    print("\n🔗 Testing Google Calendar Webhook Management Endpoints")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        headers = {"X-API-Key": TEST_API_KEY}
        
        # Test webhook status endpoint
        print("📊 Testing webhook status endpoint:")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/adapters/google_calendar/webhook_status",
                headers=headers
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Webhook status retrieved successfully")
                result = response.json()
                print(f"   Total channels: {result.get('total_channels', 0)}")
                print(f"   Expired channels: {result.get('expired_channels', 0)}")
            else:
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test cleanup endpoint
        print("\n🧹 Testing cleanup endpoint:")
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/adapters/google_calendar/cleanup_channels",
                headers=headers
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Cleanup completed successfully")
                result = response.json()
                print(f"   Cleaned channels: {result.get('cleaned_channels', 0)}")
            else:
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test health endpoint
        print("\n🏥 Testing adapter health endpoint:")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/adapters/google_calendar/health",
                headers=headers
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Health status retrieved successfully")
                result = response.json()
                print(f"   Adapter healthy: {result.get('is_healthy', False)}")
                print(f"   Webhook channels: {result.get('webhook_channels', {})}")
            else:
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")


async def test_openapi_documentation():
    """Test that OpenAPI documentation is accessible and includes auth."""
    print("\n📚 Testing OpenAPI Documentation")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        # Test OpenAPI JSON
        print("📄 Testing OpenAPI JSON:")
        try:
            response = await client.get(f"{BASE_URL}/openapi.json")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ OpenAPI JSON accessible")
                openapi_data = response.json()
                
                # Check for security schemes
                security_schemes = openapi_data.get("components", {}).get("securitySchemes", {})
                print(f"   Security schemes: {list(security_schemes.keys())}")
                
                if "BearerAuth" in security_schemes and "ApiKeyAuth" in security_schemes:
                    print("   ✅ Both authentication methods documented")
                else:
                    print("   ⚠️  Missing authentication schemes in documentation")
            else:
                print(f"   ❌ Failed to get OpenAPI JSON: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test Swagger UI
        print("\n🖥️  Testing Swagger UI:")
        try:
            response = await client.get(f"{BASE_URL}/docs")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Swagger UI accessible")
                if "Authorize" in response.text:
                    print("   ✅ Authorization button present in UI")
                else:
                    print("   ⚠️  Authorization button not found in UI")
            else:
                print(f"   ❌ Failed to get Swagger UI: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error: {e}")


async def main():
    """Main test function."""
    print("🧪 Authentication and API Documentation Test")
    print("Testing authentication fixes and enhanced Swagger documentation")
    print()
    
    try:
        # Test authentication methods
        await test_authentication_methods()
        
        # Test trigger creation with auth
        await test_trigger_creation()
        
        # Test new webhook endpoints
        await test_webhook_endpoints()
        
        # Test OpenAPI documentation
        await test_openapi_documentation()
        
        print("\n✅ All authentication tests completed!")
        print("\n📝 Summary:")
        print("   - Authentication middleware should be working")
        print("   - Both Bearer token and X-API-Key methods should be supported")
        print("   - New Google Calendar webhook management endpoints should be accessible")
        print("   - Swagger UI should include authentication options")
        print("   - OpenAPI documentation should be enhanced with proper schemas")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
